"""RAG Pipeline Service

Integrates BGE-M3 embeddings and Qdrant vector store for pharmaceutical document retrieval.
Implements production-ready RAG with comprehensive error handling and performance optimization.
"""

import asyncio
import logging
import time
from typing import List, Optional, Dict, Any, Tuple, Union
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import json
from datetime import datetime

from pydantic import BaseModel, Field, field_validator, ConfigDict
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

# Import our custom services
from .bge_m3_embeddings import (
    BGE_M3_EmbeddingService, EmbeddingRequest, EmbeddingResponse,
    get_embedding_service, EmbeddingError
)
from .qdrant_store import (
    QdrantVectorStore, VectorPoint, DocumentMetadata, SearchRequest,
    SearchResponse, get_vector_store, VectorStoreError
)

# Configure structured logging
logger = logging.getLogger(__name__)

class RAGError(Exception):
    """Custom exception for RAG pipeline operations."""
    def __init__(self, message: str, error_code: str):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class PipelineStatus(str, Enum):
    """Pipeline status enumeration."""
    NOT_INITIALIZED = "not_initialized"
    INITIALIZING = "initializing"
    READY = "ready"
    ERROR = "error"

@dataclass(frozen=True)
class RAGConfig:
    """Configuration for RAG pipeline."""
    chunk_size: int = 1000
    chunk_overlap: int = 200
    max_chunks_per_document: int = 100
    similarity_threshold: float = 0.7
    max_results: int = 10
    enable_reranking: bool = True
    rerank_top_k: int = 20
    
class DocumentInput(BaseModel):
    """Input model for document processing."""
    content: str = Field(..., description="Document content")
    title: str = Field(..., description="Document title")
    source: str = Field(..., description="Document source")
    document_type: str = Field(..., description="Document type")
    file_path: Optional[str] = Field(None, description="Original file path")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    
    @field_validator('content', 'title', 'source', 'document_type')
    @classmethod
    def validate_required_fields(cls, v: str) -> str:
        """Validate required string fields."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()
    
    @field_validator('content')
    @classmethod
    def validate_content_length(cls, v: str) -> str:
        """Validate content length."""
        if len(v) > 1_000_000:  # 1MB text limit
            raise ValueError("Document content too large (max 1MB)")
        return v

class QueryRequest(BaseModel):
    """Query request model."""
    query: str = Field(..., description="Search query")
    max_results: int = Field(default=10, ge=1, le=50, description="Maximum results to return")
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Minimum similarity score")
    filter_conditions: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")
    include_scores: bool = Field(default=True, description="Include similarity scores")
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v: str) -> str:
        """Validate and sanitize query."""
        if not v or not v.strip():
            raise ValueError("Query cannot be empty")
        
        # Sanitize query
        sanitized = v.replace('\x00', '').strip()
        if len(sanitized) > 1000:  # Reasonable query length
            sanitized = sanitized[:1000]
            logger.warning("Query truncated to 1000 characters", 
                         extra={"error_code": "QUERY_TRUNCATED"})
        
        return sanitized

class RetrievalResult(BaseModel):
    """Individual retrieval result."""
    chunk_id: str
    content: str
    score: float
    metadata: DocumentMetadata
    
class QueryResponse(BaseModel):
    """Query response model."""
    results: List[RetrievalResult]
    total_found: int
    query_time_ms: float
    embedding_time_ms: float
    search_time_ms: float
    rerank_time_ms: Optional[float] = None
    
class ProcessingResult(BaseModel):
    """Document processing result."""
    document_id: str
    chunks_created: int
    processing_time_ms: float
    embedding_time_ms: float
    storage_time_ms: float
    
class RAGPipeline:
    """RAG Pipeline with production-ready error handling and performance optimization."""
    
    def __init__(self, config: Optional[RAGConfig] = None):
        """Initialize the RAG pipeline."""
        self.config = config or RAGConfig()
        self.embedding_service = get_embedding_service()
        self.vector_store = get_vector_store()
        self.status = PipelineStatus.NOT_INITIALIZED
        self._lock = asyncio.Lock()
        
        # Initialize text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )
        
        logger.info("RAG pipeline initialized", 
                   extra={
                       "chunk_size": self.config.chunk_size,
                       "chunk_overlap": self.config.chunk_overlap,
                       "similarity_threshold": self.config.similarity_threshold
                   })
    
    async def initialize(self) -> None:
        """Initialize the RAG pipeline components."""
        async with self._lock:
            if self.status == PipelineStatus.READY:
                return
            
            if self.status == PipelineStatus.INITIALIZING:
                # Wait for initialization to complete
                while self.status == PipelineStatus.INITIALIZING:
                    await asyncio.sleep(0.1)
                return
            
            try:
                self.status = PipelineStatus.INITIALIZING
                logger.info("Initializing RAG pipeline...")
                
                # Initialize embedding service
                await self.embedding_service.load_model()
                
                # Initialize vector store
                await self.vector_store.connect()
                await self.vector_store.create_collection()
                
                self.status = PipelineStatus.READY
                logger.info("RAG pipeline initialized successfully")
                
            except Exception as e:
                self.status = PipelineStatus.ERROR
                error_code = "PIPELINE_INIT_FAILED"
                logger.error("Failed to initialize RAG pipeline", 
                           extra={"error_code": error_code})
                raise RAGError(f"Pipeline initialization failed: {str(e)}", error_code)
    
    async def process_document(self, document: DocumentInput) -> ProcessingResult:
        """Process a document and store its embeddings."""
        start_time = time.time()
        
        # Ensure pipeline is initialized
        if self.status != PipelineStatus.READY:
            await self.initialize()
        
        try:
            # Generate document ID
            document_id = f"{document.source}_{document.title}_{int(time.time())}"
            
            # Split document into chunks
            chunks = self._split_document(document.content)
            
            if len(chunks) > self.config.max_chunks_per_document:
                chunks = chunks[:self.config.max_chunks_per_document]
                logger.warning(f"Document truncated to {self.config.max_chunks_per_document} chunks", 
                             extra={"error_code": "DOCUMENT_TRUNCATED", "document_id": document_id})
            
            # Generate embeddings for all chunks
            embedding_start = time.time()
            chunk_texts = [chunk.page_content for chunk in chunks]
            embedding_request = EmbeddingRequest(texts=chunk_texts)
            embedding_response = await self.embedding_service.generate_embeddings(embedding_request)
            embedding_time = (time.time() - embedding_start) * 1000
            
            # Create vector points
            vector_points = []
            for i, (chunk, embedding) in enumerate(zip(chunks, embedding_response.embeddings)):
                metadata = DocumentMetadata(
                    document_id=document_id,
                    title=document.title,
                    source=document.source,
                    document_type=document.document_type,
                    chunk_index=i,
                    chunk_text=chunk.page_content,
                    file_path=document.file_path,
                    page_number=chunk.metadata.get('page_number'),
                    section=chunk.metadata.get('section')
                )
                
                vector_point = VectorPoint(
                    vector=embedding,
                    metadata=metadata
                )
                vector_points.append(vector_point)
            
            # Store in vector database
            storage_start = time.time()
            await self.vector_store.upsert_points(vector_points)
            storage_time = (time.time() - storage_start) * 1000
            
            processing_time = (time.time() - start_time) * 1000
            
            logger.info(f"Document processed successfully", 
                       extra={
                           "document_id": document_id,
                           "chunks_created": len(chunks),
                           "processing_time_ms": processing_time
                       })
            
            return ProcessingResult(
                document_id=document_id,
                chunks_created=len(chunks),
                processing_time_ms=processing_time,
                embedding_time_ms=embedding_time,
                storage_time_ms=storage_time
            )
            
        except Exception as e:
            error_code = "DOCUMENT_PROCESSING_FAILED"
            logger.error("Failed to process document", 
                        extra={"error_code": error_code, "title": document.title})
            raise RAGError(f"Document processing failed: {str(e)}", error_code)
    
    def _split_document(self, content: str) -> List[Document]:
        """Split document content into chunks."""
        try:
            # Create LangChain document
            doc = Document(page_content=content)
            
            # Split into chunks
            chunks = self.text_splitter.split_documents([doc])
            
            if not chunks:
                # Fallback: create single chunk if splitting fails
                chunks = [Document(page_content=content[:self.config.chunk_size])]
            
            return chunks
            
        except Exception as e:
            logger.error("Failed to split document", extra={"error_code": "DOCUMENT_SPLIT_FAILED"})
            # Fallback: create single chunk
            return [Document(page_content=content[:self.config.chunk_size])]
    
    async def query(self, request: QueryRequest) -> QueryResponse:
        """Query the RAG pipeline for relevant documents."""
        start_time = time.time()
        
        # Ensure pipeline is initialized
        if self.status != PipelineStatus.READY:
            await self.initialize()
        
        try:
            # Generate query embedding
            embedding_start = time.time()
            embedding_request = EmbeddingRequest(texts=[request.query])
            embedding_response = await self.embedding_service.generate_embeddings(embedding_request)
            query_embedding = embedding_response.embeddings[0]
            embedding_time = (time.time() - embedding_start) * 1000
            
            # Search vector store
            search_start = time.time()
            search_limit = request.max_results
            if self.config.enable_reranking:
                search_limit = min(self.config.rerank_top_k, request.max_results * 2)
            
            search_request = SearchRequest(
                query_vector=query_embedding,
                limit=search_limit,
                score_threshold=request.similarity_threshold,
                filter_conditions=request.filter_conditions
            )
            
            search_response = await self.vector_store.search(search_request)
            search_time = (time.time() - search_start) * 1000
            
            # Convert search results
            results = []
            rerank_time = None
            
            if self.config.enable_reranking and len(search_response.results) > request.max_results:
                # Simple reranking based on content relevance
                rerank_start = time.time()
                reranked_results = self._rerank_results(request.query, search_response.results)
                results = reranked_results[:request.max_results]
                rerank_time = (time.time() - rerank_start) * 1000
            else:
                results = search_response.results[:request.max_results]
            
            # Convert to retrieval results
            retrieval_results = []
            for result in results:
                retrieval_result = RetrievalResult(
                    chunk_id=result.id,
                    content=result.metadata.chunk_text,
                    score=result.score,
                    metadata=result.metadata
                )
                retrieval_results.append(retrieval_result)
            
            query_time = (time.time() - start_time) * 1000
            
            logger.info(f"Query completed", 
                       extra={
                           "query_length": len(request.query),
                           "results_count": len(retrieval_results),
                           "query_time_ms": query_time
                       })
            
            return QueryResponse(
                results=retrieval_results,
                total_found=len(retrieval_results),
                query_time_ms=query_time,
                embedding_time_ms=embedding_time,
                search_time_ms=search_time,
                rerank_time_ms=rerank_time
            )
            
        except Exception as e:
            error_code = "QUERY_FAILED"
            logger.error("Query failed", 
                        extra={"error_code": error_code, "query_length": len(request.query)})
            raise RAGError(f"Query failed: {str(e)}", error_code)
    
    def _rerank_results(self, query: str, results: List[Any]) -> List[Any]:
        """Simple reranking based on text similarity."""
        try:
            # Simple keyword-based reranking
            query_words = set(query.lower().split())
            
            def calculate_relevance_score(result) -> float:
                content_words = set(result.metadata.chunk_text.lower().split())
                overlap = len(query_words.intersection(content_words))
                return result.score + (overlap * 0.1)  # Boost score based on keyword overlap
            
            # Sort by combined score
            return sorted(results, key=calculate_relevance_score, reverse=True)
            
        except Exception as e:
            logger.warning("Reranking failed, returning original results", 
                         extra={"error_code": "RERANK_FAILED"})
            return results
    
    async def delete_document(self, document_id: str) -> Dict[str, Any]:
        """Delete all chunks for a document."""
        if not document_id:
            raise RAGError("Document ID cannot be empty", "EMPTY_DOCUMENT_ID")
        
        try:
            # Search for all chunks of this document
            search_request = SearchRequest(
                query_vector=[0.0] * 1024,  # Dummy vector
                limit=1000,  # Large limit to get all chunks
                filter_conditions={"document_id": document_id}
            )
            
            search_response = await self.vector_store.search(search_request)
            
            if not search_response.results:
                logger.warning(f"No chunks found for document {document_id}")
                return {"deleted_count": 0, "document_id": document_id}
            
            # Delete all chunks
            chunk_ids = [result.id for result in search_response.results]
            delete_result = await self.vector_store.delete_points(chunk_ids)
            
            logger.info(f"Deleted document {document_id}", 
                       extra={"document_id": document_id, "chunks_deleted": len(chunk_ids)})
            
            return {
                "deleted_count": len(chunk_ids),
                "document_id": document_id,
                "operation_id": delete_result.get("operation_id")
            }
            
        except Exception as e:
            error_code = "DOCUMENT_DELETE_FAILED"
            logger.error("Failed to delete document", 
                        extra={"error_code": error_code, "document_id": document_id})
            raise RAGError(f"Document deletion failed: {str(e)}", error_code)
    
    async def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get pipeline statistics."""
        try:
            # Get vector store info
            collection_info = await self.vector_store.get_collection_info()
            
            # Get embedding service status
            embedding_status = self.embedding_service.get_status()
            
            return {
                "status": self.status.value,
                "embedding_service": embedding_status,
                "vector_store": {
                    "points_count": collection_info.get("points_count", 0),
                    "vectors_count": collection_info.get("vectors_count", 0),
                    "status": collection_info.get("status", "unknown")
                },
                "config": {
                    "chunk_size": self.config.chunk_size,
                    "chunk_overlap": self.config.chunk_overlap,
                    "similarity_threshold": self.config.similarity_threshold,
                    "max_results": self.config.max_results
                }
            }
            
        except Exception as e:
            logger.error("Failed to get pipeline stats", extra={"error_code": "STATS_FAILED"})
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a comprehensive health check."""
        try:
            if self.status != PipelineStatus.READY:
                await self.initialize()
            
            # Check embedding service
            embedding_health = await self.embedding_service.health_check()
            
            # Check vector store
            vector_health = await self.vector_store.health_check()
            
            # Test end-to-end functionality
            test_query = QueryRequest(query="test health check", max_results=1)
            test_response = await self.query(test_query)
            
            return {
                "status": "healthy",
                "pipeline_ready": True,
                "embedding_service": embedding_health,
                "vector_store": vector_health,
                "test_query_time_ms": test_response.query_time_ms
            }
            
        except Exception as e:
            logger.error("Health check failed", extra={"error_code": "HEALTH_CHECK_FAILED"})
            return {
                "status": "unhealthy",
                "pipeline_ready": False,
                "error": str(e)
            }

# Global pipeline instance
_rag_pipeline: Optional[RAGPipeline] = None

def get_rag_pipeline() -> RAGPipeline:
    """Get the global RAG pipeline instance."""
    global _rag_pipeline
    if _rag_pipeline is None:
        _rag_pipeline = RAGPipeline()
    return _rag_pipeline

# Export main classes and functions
__all__ = [
    "RAGPipeline",
    "DocumentInput",
    "QueryRequest",
    "QueryResponse",
    "RetrievalResult",
    "ProcessingResult",
    "RAGConfig",
    "RAGError",
    "PipelineStatus",
    "get_rag_pipeline"
]