"""AI services package for VigiLens."""

# Import only from existing files
try:
    from .rag_pipeline import (
        RAGPipeline,
        RAGError,
        PipelineStatus,
        RAGConfig,
        DocumentInput,
        QueryRequest,
        RetrievalResult,
        QueryResponse,
        ProcessingResult
    )
except ImportError:
    pass

try:
    from .qdrant_store import (
        QdrantVectorStore,
        get_vector_store
    )
except ImportError:
    pass

try:
    from .bge_m3_embeddings import (
        BGE_M3_EmbeddingService
    )
except ImportError:
    pass

try:
    from .crewai_agents import (
        PharmaceuticalAgentSystem
    )
except ImportError:
    pass

try:
    from .langgraph_workflow import (
        PharmaceuticalWorkflowOrchestrator
    )
except ImportError:
    pass

# Import newly created AI service modules
try:
    from .client import (
        AIClient,
        AIModelConfig,
        AIResponse,
        get_ai_client
    )
except ImportError:
    pass

try:
    from .vector_store import (
        VectorStore,
        VectorStoreConfig,
        DocumentMetadata,
        VectorSearchResult,
        get_vector_store_instance
    )
except ImportError:
    pass

try:
    from .pdf_processor import (
        PDFProcessor,
        ProcessingStatus,
        PDFPageInfo,
        PDFDocumentInfo,
        PDFProcessingConfig,
        get_fda_pdf_processor
    )
except ImportError:
    pass

try:
    from .fda_chunker import (
        FDAChunker,
        ChunkingStrategy,
        ChunkType,
        DocumentChunk,
        FDAChunkingConfig,
        get_fda_chunker
    )
except ImportError:
    pass

try:
    from .fda_metadata import (
        FDAMetadataExtractor,
        MetadataType,
        ConfidenceLevel,
        MetadataItem,
        DocumentMetadata,
        FDAMetadataConfig,
        get_fda_metadata_extractor,
        extract_document_metadata,
        get_metadata_summary
    )
except ImportError:
    pass

__all__ = [
    # RAG Pipeline
    "RAGPipeline",
    "RAGError",
    "PipelineStatus", 
    "RAGConfig",
    "DocumentInput",
    "QueryRequest",
    "RetrievalResult",
    "QueryResponse",
    "ProcessingResult",
    # Qdrant Store
    "QdrantVectorStore",
    "get_vector_store",
    # BGE Embeddings
    "BGE_M3_EmbeddingService",
    # CrewAI Agents
    "PharmaceuticalAgentSystem",
    # LangGraph Workflow
    "PharmaceuticalWorkflowOrchestrator",
    # AI Client
    "AIClient",
    "AIModelConfig",
    "AIResponse",
    "get_ai_client",
    # Vector Store
    "VectorStore",
    "VectorStoreConfig",
    "DocumentMetadata",
    "VectorSearchResult",
    "get_vector_store_instance",
    # PDF Processor
    "PDFProcessor",
    "ProcessingStatus",
    "PDFPageInfo",
    "PDFDocumentInfo",
    "PDFProcessingConfig",
    "get_fda_pdf_processor",
    # FDA Chunker
    "FDAChunker",
    "ChunkingStrategy",
    "ChunkType",
    "DocumentChunk",
    "FDAChunkingConfig",
    "get_fda_chunker",
    # FDA Metadata
    "FDAMetadataExtractor",
    "MetadataType",
    "ConfidenceLevel",
    "MetadataItem",
    "DocumentMetadata",
    "FDAMetadataConfig",
    "get_fda_metadata_extractor",
    "extract_document_metadata",
    "get_metadata_summary",
]
