"""PDF Processor Service for VigiLens.

Provides PDF processing capabilities for FDA documents using PyMuPDF.
"""

import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

import fitz  # PyMuPDF
from pydantic import BaseModel, Field, ConfigDict

logger = logging.getLogger(__name__)


class ProcessingStatus(Enum):
    """PDF processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PDFPageInfo(BaseModel):
    """Information about a PDF page."""
    model_config = ConfigDict(arbitrary_types_allowed=True)

    page_number: int = Field(description="Page number (1-indexed)")
    text_content: str = Field(description="Extracted text content")
    word_count: int = Field(description="Number of words")
    char_count: int = Field(description="Number of characters")
    has_images: bool = Field(default=False, description="Whether page contains images")
    has_tables: bool = Field(default=False, description="Whether page contains tables")
    bbox: Optional[Tuple[float, float, float, float]] = Field(default=None, description="Page bounding box")


class PDFDocumentInfo(BaseModel):
    """Information about a PDF document."""
    model_config = ConfigDict(arbitrary_types_allowed=True)

    file_path: str = Field(description="Path to PDF file")
    file_name: str = Field(description="PDF file name")
    file_size: int = Field(description="File size in bytes")
    page_count: int = Field(description="Number of pages")
    title: Optional[str] = Field(default=None, description="Document title")
    author: Optional[str] = Field(default=None, description="Document author")
    subject: Optional[str] = Field(default=None, description="Document subject")
    creator: Optional[str] = Field(default=None, description="Document creator")
    producer: Optional[str] = Field(default=None, description="Document producer")
    creation_date: Optional[str] = Field(default=None, description="Creation date")
    modification_date: Optional[str] = Field(default=None, description="Modification date")
    is_encrypted: bool = Field(default=False, description="Whether document is encrypted")
    total_words: int = Field(default=0, description="Total word count")
    total_chars: int = Field(default=0, description="Total character count")


class PDFProcessingConfig(BaseModel):
    """Configuration for PDF processing."""
    model_config = ConfigDict(arbitrary_types_allowed=True)

    extract_images: bool = Field(default=False, description="Extract images from PDF")
    extract_tables: bool = Field(default=False, description="Extract tables from PDF")
    preserve_layout: bool = Field(default=True, description="Preserve text layout")
    min_word_length: int = Field(default=2, ge=1, description="Minimum word length")
    max_page_size: int = Field(default=50000, ge=1000, description="Maximum characters per page")
    skip_empty_pages: bool = Field(default=True, description="Skip pages with no text")
    clean_text: bool = Field(default=True, description="Clean extracted text")
    extract_metadata: bool = Field(default=True, description="Extract document metadata")


class PDFProcessor:
    """PDF processor for FDA documents."""

    def __init__(self, config: Optional[PDFProcessingConfig] = None):
        """Initialize PDF processor."""
        self.config = config or PDFProcessingConfig()
        self._processing_status = ProcessingStatus.PENDING

    def get_processing_status(self) -> ProcessingStatus:
        """Get current processing status."""
        return self._processing_status

    def extract_document_info(self, file_path: str) -> PDFDocumentInfo:
        """Extract basic document information."""
        try:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"PDF file not found: {file_path}")

            doc = fitz.open(file_path)
            metadata = doc.metadata

            # Calculate total text statistics
            total_words = 0
            total_chars = 0

            for page_num in range(doc.page_count):
                page = doc[page_num]
                text = page.get_text()
                total_chars += len(text)
                total_words += len(text.split())

            doc_info = PDFDocumentInfo(
                file_path=str(file_path_obj.absolute()),
                file_name=file_path_obj.name,
                file_size=file_path_obj.stat().st_size,
                page_count=doc.page_count,
                title=metadata.get('title'),
                author=metadata.get('author'),
                subject=metadata.get('subject'),
                creator=metadata.get('creator'),
                producer=metadata.get('producer'),
                creation_date=metadata.get('creationDate'),
                modification_date=metadata.get('modDate'),
                is_encrypted=doc.needs_pass,
                total_words=total_words,
                total_chars=total_chars
            )

            doc.close()
            return doc_info

        except Exception as e:
            logger.error(f"Failed to extract document info from {file_path}: {e}")
            raise

    def extract_text_from_page(self, file_path: str, page_number: int) -> PDFPageInfo:
        """Extract text from a specific page."""
        try:
            doc = fitz.open(file_path)

            if page_number < 1 or page_number > doc.page_count:
                raise ValueError(f"Invalid page number: {page_number}")

            page = doc[page_number - 1]  # Convert to 0-indexed

            # Extract text using get_text() method (not extract_text())
            if self.config.preserve_layout:
                text = page.get_text("text")
            else:
                text = page.get_text()

            # Clean text if configured
            if self.config.clean_text:
                text = self._clean_text(text)

            # Check for images and tables
            has_images = len(page.get_images()) > 0
            has_tables = self._detect_tables(page)

            page_info = PDFPageInfo(
                page_number=page_number,
                text_content=text,
                word_count=len(text.split()),
                char_count=len(text),
                has_images=has_images,
                has_tables=has_tables,
                bbox=tuple(page.rect) if page.rect else None
            )

            doc.close()
            return page_info

        except Exception as e:
            logger.error(f"Failed to extract text from page {page_number} of {file_path}: {e}")
            raise

    def extract_all_text(self, file_path: str) -> List[PDFPageInfo]:
        """Extract text from all pages."""
        try:
            self._processing_status = ProcessingStatus.PROCESSING

            doc_info = self.extract_document_info(file_path)
            pages = []

            for page_num in range(1, doc_info.page_count + 1):
                try:
                    page_info = self.extract_text_from_page(file_path, page_num)

                    # Skip empty pages if configured
                    if self.config.skip_empty_pages and not page_info.text_content.strip():
                        continue

                    pages.append(page_info)

                except Exception as e:
                    logger.warning(f"Failed to process page {page_num}: {e}")
                    continue

            self._processing_status = ProcessingStatus.COMPLETED
            logger.info(f"Extracted text from {len(pages)} pages of {file_path}")
            return pages

        except Exception as e:
            self._processing_status = ProcessingStatus.FAILED
            logger.error(f"Failed to extract all text from {file_path}: {e}")
            raise

    def _clean_text(self, text: str) -> str:
        """Clean extracted text."""
        if not text:
            return text

        # Remove excessive whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)

        # Remove very short words if configured
        if self.config.min_word_length > 1:
            words = text.split()
            words = [word for word in words if len(word) >= self.config.min_word_length]
            text = ' '.join(words)

        return text.strip()

    def _detect_tables(self, page) -> bool:
        """Detect if page contains tables."""
        try:
            # Simple table detection based on text layout
            text = page.get_text("dict")

            # Look for table-like structures
            # This is a simplified detection - could be enhanced
            blocks = text.get("blocks", [])

            for block in blocks:
                if "lines" in block:
                    lines = block["lines"]
                    if len(lines) > 3:  # Potential table with multiple rows
                        # Check for consistent spacing/alignment
                        return True

            return False

        except Exception:
            return False


# Global processor instance
_global_processor: Optional[PDFProcessor] = None


def get_fda_pdf_processor(config: Optional[PDFProcessingConfig] = None) -> PDFProcessor:
    """Get FDA PDF processor instance."""
    global _global_processor

    if _global_processor is None or config is not None:
        processor_config = config or PDFProcessingConfig(
            extract_images=os.getenv("PDF_EXTRACT_IMAGES", "false").lower() == "true",
            extract_tables=os.getenv("PDF_EXTRACT_TABLES", "false").lower() == "true",
            preserve_layout=os.getenv("PDF_PRESERVE_LAYOUT", "true").lower() == "true",
            min_word_length=int(os.getenv("PDF_MIN_WORD_LENGTH", "2")),
            max_page_size=int(os.getenv("PDF_MAX_PAGE_SIZE", "50000")),
            skip_empty_pages=os.getenv("PDF_SKIP_EMPTY_PAGES", "true").lower() == "true",
            clean_text=os.getenv("PDF_CLEAN_TEXT", "true").lower() == "true",
            extract_metadata=os.getenv("PDF_EXTRACT_METADATA", "true").lower() == "true"
        )

        _global_processor = PDFProcessor(processor_config)

    return _global_processor
