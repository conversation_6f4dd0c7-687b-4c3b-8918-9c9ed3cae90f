"""AI Client Service for VigiLens.

Provides unified interface for AI model interactions using OpenRouter + MoonshotAI Kimi K2.
"""

import asyncio
import logging
import os
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from dataclasses import dataclass

import httpx
from pydantic import BaseModel, Field, ConfigDict
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)


class ConnectionStatus(Enum):
    """AI client connection status."""
    CONNECTED = "connected"
    NOT_CONNECTED = "not_connected"
    ERROR = "error"
    INITIALIZING = "initializing"


class AIModelConfig(BaseModel):
    """Configuration for AI model."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    model_name: str = Field(default="moonshotai/kimi-k2:free", description="Model name")
    api_key: str = Field(description="API key for the service")
    base_url: str = Field(default="https://openrouter.ai/api/v1", description="Base URL")
    max_tokens: int = Field(default=4096, ge=1, le=32768, description="Maximum tokens")
    temperature: float = Field(default=0.1, ge=0.0, le=2.0, description="Temperature")
    timeout: int = Field(default=30, ge=1, le=300, description="Request timeout in seconds")
    max_retries: int = Field(default=3, ge=1, le=10, description="Maximum retry attempts")


class AIResponse(BaseModel):
    """Response from AI model."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    content: str = Field(description="Response content")
    model: str = Field(description="Model used")
    usage: Optional[Dict[str, Any]] = Field(default=None, description="Token usage info")
    finish_reason: Optional[str] = Field(default=None, description="Completion reason")
    response_time: Optional[float] = Field(default=None, description="Response time in seconds")


class AIClient:
    """AI client for OpenRouter + MoonshotAI integration."""
    
    def __init__(self, config: AIModelConfig):
        """Initialize AI client."""
        self.config = config
        self._client: Optional[httpx.AsyncClient] = None
        self._status = ConnectionStatus.INITIALIZING
        self._last_error: Optional[str] = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
        
    async def connect(self) -> bool:
        """Connect to AI service."""
        try:
            self._client = httpx.AsyncClient(
                base_url=self.config.base_url,
                timeout=httpx.Timeout(self.config.timeout),
                headers={
                    "Authorization": f"Bearer {self.config.api_key}",
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://vigilens.ai",
                    "X-Title": "VigiLens Pharmaceutical Compliance"
                }
            )
            
            # Test connection
            response = await self._client.get("/models")
            if response.status_code == 200:
                self._status = ConnectionStatus.CONNECTED
                logger.info(f"Connected to AI service: {self.config.base_url}")
                return True
            else:
                self._status = ConnectionStatus.ERROR
                self._last_error = f"Connection test failed: {response.status_code}"
                return False
                
        except Exception as e:
            self._status = ConnectionStatus.ERROR
            self._last_error = str(e)
            logger.error(f"Failed to connect to AI service: {e}")
            return False
            
    async def disconnect(self):
        """Disconnect from AI service."""
        if self._client:
            await self._client.aclose()
            self._client = None
        self._status = ConnectionStatus.NOT_CONNECTED
        
    def get_service_status(self) -> ConnectionStatus:
        """Get current service status."""
        return self._status
        
    def get_last_error(self) -> Optional[str]:
        """Get last error message."""
        return self._last_error
        
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> AIResponse:
        """Generate response from AI model."""
        if not self._client or self._status != ConnectionStatus.CONNECTED:
            raise RuntimeError("AI client not connected")
            
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": self.config.model_name,
            "messages": messages,
            "max_tokens": max_tokens or self.config.max_tokens,
            "temperature": temperature or self.config.temperature,
            **kwargs
        }
        
        import time
        start_time = time.time()
        
        try:
            response = await self._client.post("/chat/completions", json=payload)
            response.raise_for_status()
            
            data = response.json()
            response_time = time.time() - start_time
            
            choice = data["choices"][0]
            return AIResponse(
                content=choice["message"]["content"],
                model=data["model"],
                usage=data.get("usage"),
                finish_reason=choice.get("finish_reason"),
                response_time=response_time
            )
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error in AI request: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Error in AI request: {e}")
            raise
            
    async def generate_streaming_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ):
        """Generate streaming response from AI model."""
        if not self._client or self._status != ConnectionStatus.CONNECTED:
            raise RuntimeError("AI client not connected")
            
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": self.config.model_name,
            "messages": messages,
            "max_tokens": max_tokens or self.config.max_tokens,
            "temperature": temperature or self.config.temperature,
            "stream": True,
            **kwargs
        }
        
        async with self._client.stream("POST", "/chat/completions", json=payload) as response:
            response.raise_for_status()
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data_str = line[6:]
                    if data_str.strip() == "[DONE]":
                        break
                    try:
                        import json
                        data = json.loads(data_str)
                        if "choices" in data and data["choices"]:
                            delta = data["choices"][0].get("delta", {})
                            if "content" in delta:
                                yield delta["content"]
                    except json.JSONDecodeError:
                        continue


# Global client instance
_global_client: Optional[AIClient] = None


async def get_ai_client() -> AIClient:
    """Get global AI client instance."""
    global _global_client
    
    if _global_client is None:
        config = AIModelConfig(
            api_key=os.getenv("OPENROUTER_API_KEY", ""),
            model_name=os.getenv("OPENROUTER_MODEL", "moonshotai/kimi-k2:free"),
            base_url=os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1"),
            max_tokens=int(os.getenv("MAX_TOKENS", "4096")),
            temperature=float(os.getenv("TEMPERATURE", "0.1")),
            timeout=int(os.getenv("AI_TIMEOUT", "30"))
        )
        
        _global_client = AIClient(config)
        await _global_client.connect()
        
    return _global_client


async def get_client() -> AIClient:
    """Alias for get_ai_client."""
    return await get_ai_client()