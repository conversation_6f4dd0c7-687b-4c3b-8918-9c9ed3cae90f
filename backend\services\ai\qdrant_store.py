"""Qdrant Vector Store Service

Provides high-performance vector storage and retrieval using Qdrant.
Implements production-ready patterns with comprehensive error handling.
"""

import asyncio
import logging
import uuid
from typing import List, Optional, Dict, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import json
from datetime import datetime

from qdrant_client import AsyncQdrantClient, models
from qdrant_client.models import (
    Distance,
    VectorParams,
    PointStruct,
    SearchRequest as QdrantSearchRequest,
    Filter,
    FieldCondition,
    MatchValue,
    PointIdsList
)
from pydantic import BaseModel, Field, field_validator, ConfigDict

# Configure structured logging
logger = logging.getLogger(__name__)

class VectorStoreError(Exception):
    """Custom exception for vector store operations."""
    def __init__(self, message: str, error_code: str):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class ConnectionStatus(str, Enum):
    """Connection status enumeration."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

@dataclass
class QdrantConfig:
    """Configuration for Qdrant vector store."""
    host: str = "localhost"
    port: int = 6333
    grpc_port: int = 6334
    prefer_grpc: bool = True
    timeout: int = 60  # Changed from float to int
    api_key: Optional[str] = None
    https: bool = False
    local_path: Optional[str] = "./data/qdrant"  # Local file-based storage
    collection_name: str = "pharmaceutical_documents"
    vector_size: int = 1024  # BGE-M3 dimension
    distance_metric: Distance = Distance.COSINE

class DocumentMetadata(BaseModel):
    """Metadata for stored documents."""
    document_id: str = Field(..., description="Unique document identifier")
    title: str = Field(..., description="Document title")
    source: str = Field(..., description="Document source (FDA, EMA, etc.)")
    document_type: str = Field(..., description="Type of document")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    chunk_index: int = Field(..., ge=0, description="Chunk index within document")
    chunk_text: str = Field(..., description="Text content of the chunk")
    file_path: Optional[str] = Field(None, description="Original file path")
    page_number: Optional[int] = Field(None, ge=1, description="Page number if applicable")
    section: Optional[str] = Field(None, description="Document section")

    @field_validator('document_id', 'title', 'source', 'document_type')
    @classmethod
    def validate_required_strings(cls, v: str) -> str:
        """Validate required string fields."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()

    @field_validator('chunk_text')
    @classmethod
    def validate_chunk_text(cls, v: str) -> str:
        """Validate and sanitize chunk text."""
        if not v or not v.strip():
            raise ValueError("Chunk text cannot be empty")

        # Sanitize text - remove null bytes and control characters
        sanitized = v.replace('\x00', '').strip()
        if len(sanitized) > 10000:  # Reasonable chunk size limit
            sanitized = sanitized[:10000]
            logger.warning("Chunk text truncated to 10000 characters",
                         extra={"error_code": "CHUNK_TEXT_TRUNCATED"})

        return sanitized

class VectorPoint(BaseModel):
    """Vector point for storage."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    vector: List[float] = Field(..., description="Embedding vector")
    metadata: DocumentMetadata = Field(..., description="Document metadata")

    @field_validator('vector')
    @classmethod
    def validate_vector(cls, v: List[float]) -> List[float]:
        """Validate vector dimensions and values."""
        if not v:
            raise ValueError("Vector cannot be empty")

        if len(v) != 1024:  # BGE-M3 dimension
            raise ValueError(f"Vector must have 1024 dimensions, got {len(v)}")

        # Check for invalid values
        for i, val in enumerate(v):
            if not isinstance(val, (int, float)):
                raise ValueError(f"Vector value at index {i} must be numeric")
            if not (-1e6 <= val <= 1e6):  # Reasonable bounds
                raise ValueError(f"Vector value at index {i} is out of bounds: {val}")

        return v

class SearchRequest(BaseModel):
    """Search request model."""
    query_vector: List[float] = Field(..., description="Query embedding vector")
    limit: int = Field(default=10, ge=1, le=100, description="Number of results to return")
    score_threshold: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum similarity score")
    filter_conditions: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")

    @field_validator('query_vector')
    @classmethod
    def validate_query_vector(cls, v: List[float]) -> List[float]:
        """Validate query vector."""
        if not v or len(v) != 1024:
            raise ValueError("Query vector must have 1024 dimensions")
        return v

class SearchResult(BaseModel):
    """Search result model."""
    id: str
    score: float
    metadata: DocumentMetadata

class SearchResponse(BaseModel):
    """Search response model."""
    results: List[SearchResult]
    total_found: int
    search_time_ms: float

class QdrantVectorStore:
    """Qdrant Vector Store with production-ready error handling."""

    def __init__(self, config: Optional[QdrantConfig] = None):
        """Initialize the Qdrant vector store."""
        self.config = config or QdrantConfig()
        self.client: Optional[AsyncQdrantClient] = None
        self.status = ConnectionStatus.DISCONNECTED
        self._lock = asyncio.Lock()

        logger.info(f"Qdrant store initialized",
                   extra={
                       "host": self.config.host,
                       "port": self.config.port,
                       "collection": self.config.collection_name
                   })

    async def _ensure_client(self) -> AsyncQdrantClient:
        """Ensure client is connected and return it."""
        if self.client is None:
            await self.connect()
        if self.client is None:
            raise VectorStoreError("Failed to initialize Qdrant client", "CLIENT_INIT_FAILED")
        return self.client

    async def connect(self) -> None:
        """Connect to Qdrant server."""
        async with self._lock:
            if self.status == ConnectionStatus.CONNECTED:
                return

            if self.status == ConnectionStatus.CONNECTING:
                # Wait for connection to complete
                while self.status == ConnectionStatus.CONNECTING:
                    await asyncio.sleep(0.1)
                return

            try:
                self.status = ConnectionStatus.CONNECTING
                logger.info("Connecting to Qdrant...",
                           extra={"host": self.config.host, "port": self.config.port})

                # Create client in thread pool
                loop = asyncio.get_event_loop()
                self.client = await loop.run_in_executor(
                    None,
                    self._create_client
                )

                # Test connection
                await self.client.get_collections()

                self.status = ConnectionStatus.CONNECTED
                logger.info("Connected to Qdrant successfully",
                           extra={"host": self.config.host, "port": self.config.port})

            except Exception as e:
                self.status = ConnectionStatus.ERROR
                error_code = "CONNECTION_FAILED"
                logger.error("Failed to connect to Qdrant",
                           extra={"error_code": error_code, "host": self.config.host})
                raise VectorStoreError(f"Connection failed: {str(e)}", error_code)

    def _create_client(self) -> AsyncQdrantClient:
        """Create Qdrant client synchronously."""
        # Use local file-based storage if local_path is provided
        if self.config.local_path:
            from pathlib import Path
            Path(self.config.local_path).mkdir(parents=True, exist_ok=True)
            return AsyncQdrantClient(
                path=self.config.local_path,
                prefer_grpc=self.config.prefer_grpc,
                timeout=self.config.timeout
            )
        elif self.config.api_key:
            return AsyncQdrantClient(
                url=f"{'https' if self.config.https else 'http'}://{self.config.host}:{self.config.port}",
                api_key=self.config.api_key,
                timeout=self.config.timeout
            )
        else:
            return AsyncQdrantClient(
                host=self.config.host,
                port=self.config.port,
                grpc_port=self.config.grpc_port,
                prefer_grpc=self.config.prefer_grpc,
                timeout=self.config.timeout
            )

    async def create_collection(self) -> None:
        """Create the collection if it doesn't exist."""
        client = await self._ensure_client()

        try:
            # Check if collection exists
            collection_exists = await client.collection_exists(self.config.collection_name)

            if collection_exists:
                logger.info(f"Collection '{self.config.collection_name}' already exists")
                return

            # Create collection
            await client.create_collection(
                collection_name=self.config.collection_name,
                vectors_config=models.VectorParams(
                    size=self.config.vector_size,
                    distance=self.config.distance_metric
                )
            )

            logger.info(f"Created collection '{self.config.collection_name}'",
                       extra={
                           "collection_name": self.config.collection_name,
                           "vector_size": self.config.vector_size,
                           "distance_metric": self.config.distance_metric.value
                       })

        except Exception as e:
            error_code = "COLLECTION_CREATION_FAILED"
            logger.error(f"Failed to create collection",
                        extra={"error_code": error_code, "collection_name": self.config.collection_name})
            raise VectorStoreError(f"Collection creation failed: {str(e)}", error_code)

    async def upsert_points(self, points: List[VectorPoint]) -> Dict[str, Any]:
        """Insert or update vector points."""
        if not points:
            raise VectorStoreError("Points list cannot be empty", "EMPTY_POINTS_LIST")

        client = await self._ensure_client()
        await self.create_collection()

        try:
            import time
            start_time = time.time()

            # Convert to Qdrant points
            qdrant_points = []
            for point in points:
                qdrant_point = models.PointStruct(
                    id=point.id,
                    vector=point.vector,
                    payload=point.metadata.dict()
                )
                qdrant_points.append(qdrant_point)

            # Upsert points
            result = await client.upsert(
                collection_name=self.config.collection_name,
                points=qdrant_points
            )

            processing_time = (time.time() - start_time) * 1000

            logger.info(f"Upserted {len(points)} points",
                       extra={
                           "collection_name": self.config.collection_name,
                           "points_count": len(points),
                           "processing_time_ms": processing_time
                       })

            return {
                "operation_id": getattr(result, 'operation_id', None),
                "status": getattr(result, 'status', "completed"),
                "points_count": len(points),
                "processing_time_ms": processing_time
            }

        except Exception as e:
            error_code = "UPSERT_FAILED"
            logger.error("Failed to upsert points",
                        extra={"error_code": error_code, "points_count": len(points)})
            raise VectorStoreError(f"Upsert failed: {str(e)}", error_code)

    async def search(self, request: SearchRequest) -> SearchResponse:
        """Search for similar vectors."""
        import time
        start_time = time.time()

        client = await self._ensure_client()

        try:
            # Build filter if provided
            query_filter = None
            if request.filter_conditions:
                conditions = []
                for key, value in request.filter_conditions.items():
                    conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value)
                        )
                    )
                query_filter = Filter(must=conditions)

            # Perform search
            search_result = await client.search(
                collection_name=self.config.collection_name,
                query_vector=request.query_vector,
                query_filter=query_filter,
                limit=request.limit,
                score_threshold=request.score_threshold
            )

            # Convert results
            results = []
            for hit in search_result:
                metadata = DocumentMetadata(**hit.payload)
                result = SearchResult(
                    id=str(hit.id),
                    score=hit.score,
                    metadata=metadata
                )
                results.append(result)

            search_time = (time.time() - start_time) * 1000

            logger.info(f"Search completed",
                       extra={
                           "collection_name": self.config.collection_name,
                           "results_count": len(results),
                           "search_time_ms": search_time
                       })

            return SearchResponse(
                results=results,
                total_found=len(results),
                search_time_ms=search_time
            )

        except Exception as e:
            error_code = "SEARCH_FAILED"
            logger.error("Search failed",
                        extra={"error_code": error_code, "collection_name": self.config.collection_name})
            raise VectorStoreError(f"Search failed: {str(e)}", error_code)

    async def delete_points(self, point_ids: List[str]) -> Dict[str, Any]:
        """Delete points by IDs."""
        if not point_ids:
            raise VectorStoreError("Point IDs list cannot be empty", "EMPTY_IDS_LIST")

        client = await self._ensure_client()

        try:
            result = await client.delete(
                collection_name=self.config.collection_name,
                points_selector=PointIdsList(points=point_ids)
            )

            logger.info(f"Deleted {len(point_ids)} points",
                       extra={
                           "collection_name": self.config.collection_name,
                           "deleted_count": len(point_ids)
                       })

            return {
                "operation_id": result.operation_id,
                "status": result.status.value,
                "deleted_count": len(point_ids)
            }

        except Exception as e:
            error_code = "DELETE_FAILED"
            logger.error("Failed to delete points",
                        extra={"error_code": error_code, "ids_count": len(point_ids)})
            raise VectorStoreError(f"Delete failed: {str(e)}", error_code)

    async def get_collection_info(self) -> Dict[str, Any]:
        """Get collection information."""
        client = await self._ensure_client()

        try:
            info = await client.get_collection(
                collection_name=self.config.collection_name
            )

            # Safely access vector configuration
            vector_size = None
            distance = None
            if hasattr(info.config, 'params') and hasattr(info.config.params, 'vectors'):
                vectors_config = info.config.params.vectors
                if isinstance(vectors_config, dict):
                    # Handle dictionary format
                    default_vector = vectors_config.get('', vectors_config.get('default'))
                    if default_vector:
                        vector_size = getattr(default_vector, 'size', None)
                        distance = getattr(default_vector, 'distance', None)
                else:
                    # Handle direct VectorParams object
                    vector_size = getattr(vectors_config, 'size', None)
                    distance = getattr(vectors_config, 'distance', None)

            return {
                "name": self.config.collection_name,
                "vector_size": vector_size,
                "distance": distance.value if distance else None,
                "vectors_count": getattr(info, 'vectors_count', 0),
                "indexed_vectors_count": getattr(info, 'indexed_vectors_count', 0),
                "points_count": getattr(info, 'points_count', 0),
                "segments_count": getattr(info, 'segments_count', 0),
                "status": info.status.value if hasattr(info, 'status') else 'unknown'
            }

        except Exception as e:
            error_code = "INFO_RETRIEVAL_FAILED"
            logger.error("Failed to get collection info",
                        extra={"error_code": error_code, "collection_name": self.config.collection_name})
            raise VectorStoreError(f"Info retrieval failed: {str(e)}", error_code)

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the vector store."""
        try:
            if self.status != ConnectionStatus.CONNECTED:
                await self.connect()

            # Test with collection info
            info = await self.get_collection_info()

            return {
                "status": "healthy",
                "connected": True,
                "collection_exists": True,
                "points_count": info.get("points_count", 0)
            }

        except Exception as e:
            logger.error("Health check failed", extra={"error_code": "HEALTH_CHECK_FAILED"})
            return {
                "status": "unhealthy",
                "connected": False,
                "error": str(e)
            }

    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the vector store."""
        return {
            "status": self.status.value,
            "host": self.config.host,
            "port": self.config.port,
            "collection_name": self.config.collection_name,
            "vector_size": self.config.vector_size,
            "distance_metric": self.config.distance_metric.value
        }

# Global service instance
vector_store_service = QdrantVectorStore()

def get_vector_store() -> QdrantVectorStore:
    """Get the global vector store instance."""
    return vector_store_service

# Export main classes and functions
__all__ = [
    "QdrantVectorStore",
    "VectorPoint",
    "DocumentMetadata",
    "SearchRequest",
    "SearchResponse",
    "SearchResult",
    "QdrantConfig",
    "VectorStoreError",
    "ConnectionStatus",
    "vector_store_service",
    "get_vector_store"
]
