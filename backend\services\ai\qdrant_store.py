"""
Qdrant Vector Store Service

High-performance vector database service using Qdrant for pharmaceutical compliance documents.
Provides efficient vector storage, similarity search, and metadata filtering.

Features:
- Qdrant client integration with connection pooling
- Async operations with proper error handling
- Type safety with Pydantic validation
- Comprehensive logging and monitoring
- Efficient batch operations and filtering
"""

import asyncio
import logging
import uuid
from typing import List, Optional, Dict, Any, Union
from pathlib import Path
import numpy as np
from pydantic import BaseModel, Field, validator
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue

# Configure logging
logger = logging.getLogger(__name__)

class DocumentMetadata(BaseModel):
    """Metadata model for stored documents."""
    source: str = Field(..., description="Document source identifier")
    title: str = Field(..., description="Document title")
    content_type: str = Field(default="text", description="Content type")
    chunk_index: int = Field(default=0, ge=0, description="Chunk index for large documents")
    total_chunks: int = Field(default=1, ge=1, description="Total number of chunks")
    created_at: str = Field(..., description="Creation timestamp")
    regulatory_framework: Optional[str] = Field(None, description="Regulatory framework (FDA, EMA, etc.)")
    document_type: Optional[str] = Field(None, description="Document type (guidance, regulation, etc.)")
    
    @validator('source', 'title')
    def validate_non_empty_strings(cls, v):
        """Validate that required strings are not empty."""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()

class VectorSearchRequest(BaseModel):
    """Request model for vector similarity search."""
    query_vector: List[float] = Field(..., description="Query vector for similarity search")
    limit: int = Field(default=10, ge=1, le=100, description="Maximum number of results")
    score_threshold: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum similarity score")
    filter_metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")
    
    @validator('query_vector')
    def validate_query_vector(cls, v):
        """Validate query vector dimensions."""
        if not v:
            raise ValueError("Query vector cannot be empty")
        if len(v) != 1024:  # BGE-M3 dimension
            raise ValueError(f"Query vector must have 1024 dimensions, got {len(v)}")
        return v

class VectorSearchResult(BaseModel):
    """Result model for vector similarity search."""
    id: str
    score: float
    content: str
    metadata: DocumentMetadata

class VectorSearchResponse(BaseModel):
    """Response model for vector similarity search."""
    results: List[VectorSearchResult]
    total_found: int
    search_time: float

class QdrantError(Exception):
    """Custom exception for Qdrant operations."""
    pass

class QdrantVectorStore:
    """
    Qdrant Vector Store Service
    
    Provides high-performance vector storage and similarity search for pharmaceutical documents.
    Supports metadata filtering, batch operations, and efficient indexing.
    """
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 6333,
        collection_name: str = "pharmaceutical_documents",
        vector_size: int = 1024,
        distance: Distance = Distance.COSINE
    ):
        """
        Initialize Qdrant vector store.
        
        Args:
            host: Qdrant server host
            port: Qdrant server port
            collection_name: Name of the vector collection
            vector_size: Dimension of vectors (BGE-M3 = 1024)
            distance: Distance metric for similarity search
        """
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.vector_size = vector_size
        self.distance = distance
        self.client: Optional[QdrantClient] = None
        self._is_initialized = False
        
        logger.info(f"Initializing Qdrant store: {host}:{port}/{collection_name}")
    
    async def initialize(self) -> None:
        """Initialize Qdrant client and collection."""
        if self._is_initialized:
            return
        
        try:
            logger.info("Connecting to Qdrant server...")
            
            # Initialize client
            self.client = QdrantClient(host=self.host, port=self.port)
            
            # Check if collection exists, create if not
            collections = self.client.get_collections().collections
            collection_exists = any(col.name == self.collection_name for col in collections)
            
            if not collection_exists:
                logger.info(f"Creating collection: {self.collection_name}")
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=self.distance
                    )
                )
            
            self._is_initialized = True
            logger.info("Qdrant vector store initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant: {e}")
            raise QdrantError(f"Initialization failed: {e}")
    
    async def add_documents(
        self,
        contents: List[str],
        vectors: List[List[float]],
        metadatas: List[DocumentMetadata]
    ) -> List[str]:
        """
        Add documents to the vector store.
        
        Args:
            contents: List of document contents
            vectors: List of embedding vectors
            metadatas: List of document metadata
            
        Returns:
            List of document IDs
            
        Raises:
            QdrantError: If document addition fails
        """
        if not self._is_initialized:
            await self.initialize()
        
        if not self.client:
            raise QdrantError("Client not initialized")
        
        if len(contents) != len(vectors) != len(metadatas):
            raise QdrantError("Contents, vectors, and metadata lists must have same length")
        
        try:
            # Generate unique IDs for documents
            document_ids = [str(uuid.uuid4()) for _ in contents]
            
            # Prepare points for insertion
            points = []
            for i, (content, vector, metadata) in enumerate(zip(contents, vectors, metadatas)):
                # Validate vector dimensions
                if len(vector) != self.vector_size:
                    raise QdrantError(f"Vector dimension mismatch: expected {self.vector_size}, got {len(vector)}")
                
                # Prepare payload with content and metadata
                payload = {
                    "content": content,
                    **metadata.dict()
                }
                
                points.append(PointStruct(
                    id=document_ids[i],
                    vector=vector,
                    payload=payload
                ))
            
            # Insert points in batches
            batch_size = 100
            for i in range(0, len(points), batch_size):
                batch_points = points[i:i + batch_size]
                self.client.upsert(
                    collection_name=self.collection_name,
                    points=batch_points
                )
            
            logger.info(f"Added {len(document_ids)} documents to Qdrant")
            return document_ids
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise QdrantError(f"Document addition failed: {e}")
    
    async def search_similar(
        self,
        request: VectorSearchRequest
    ) -> VectorSearchResponse:
        """
        Search for similar documents.
        
        Args:
            request: Search request with query vector and parameters
            
        Returns:
            VectorSearchResponse with search results
            
        Raises:
            QdrantError: If search fails
        """
        if not self._is_initialized:
            await self.initialize()
        
        if not self.client:
            raise QdrantError("Client not initialized")
        
        try:
            import time
            start_time = time.time()
            
            # Prepare search filter
            search_filter = None
            if request.filter_metadata:
                conditions = []
                for key, value in request.filter_metadata.items():
                    conditions.append(FieldCondition(
                        key=key,
                        match=MatchValue(value=value)
                    ))
                search_filter = Filter(must=conditions)
            
            # Perform search
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=request.query_vector,
                limit=request.limit,
                score_threshold=request.score_threshold,
                query_filter=search_filter,
                with_payload=True
            )
            
            search_time = time.time() - start_time
            
            # Process results
            results = []
            for result in search_results:
                payload = result.payload
                metadata = DocumentMetadata(
                    source=payload["source"],
                    title=payload["title"],
                    content_type=payload.get("content_type", "text"),
                    chunk_index=payload.get("chunk_index", 0),
                    total_chunks=payload.get("total_chunks", 1),
                    created_at=payload["created_at"],
                    regulatory_framework=payload.get("regulatory_framework"),
                    document_type=payload.get("document_type")
                )
                
                results.append(VectorSearchResult(
                    id=str(result.id),
                    score=result.score,
                    content=payload["content"],
                    metadata=metadata
                ))
            
            logger.info(f"Found {len(results)} similar documents in {search_time:.2f}s")
            
            return VectorSearchResponse(
                results=results,
                total_found=len(results),
                search_time=search_time
            )
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise QdrantError(f"Search operation failed: {e}")
    
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """Delete documents by IDs."""
        if not self._is_initialized:
            await self.initialize()
        
        if not self.client:
            raise QdrantError("Client not initialized")
        
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=document_ids
                )
            )
            
            logger.info(f"Deleted {len(document_ids)} documents")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            raise QdrantError(f"Document deletion failed: {e}")
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection."""
        if not self._is_initialized:
            await self.initialize()
        
        if not self.client:
            raise QdrantError("Client not initialized")
        
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                "name": self.collection_name,
                "vectors_count": info.vectors_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "points_count": info.points_count,
                "status": info.status,
                "optimizer_status": info.optimizer_status
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            raise QdrantError(f"Collection info retrieval failed: {e}")
    
    async def health_check(self) -> bool:
        """Check if the vector store is healthy."""
        try:
            if not self._is_initialized:
                await self.initialize()
            
            # Test with collection info
            await self.get_collection_info()
            return True
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

# Global service instance
_vector_store: Optional[QdrantVectorStore] = None

async def get_vector_store() -> QdrantVectorStore:
    """Get or create the global vector store instance."""
    global _vector_store
    
    if _vector_store is None:
        _vector_store = QdrantVectorStore()
        await _vector_store.initialize()
    
    return _vector_store
