"""BGE-M3 Embeddings Service

Provides high-quality multilingual embeddings using the BGE-M3 model.
Follows production-first mindset with comprehensive error handling and type safety.
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass
from enum import Enum
import numpy as np
from sentence_transformers import SentenceTransformer
import torch
from pydantic import BaseModel, Field, field_validator, ConfigDict

# Configure structured logging
logger = logging.getLogger(__name__)

class EmbeddingError(Exception):
    """Custom exception for embedding-related errors."""
    def __init__(self, message: str, error_code: str):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class ModelStatus(str, Enum):
    """Model loading status enumeration."""
    NOT_LOADED = "not_loaded"
    LOADING = "loading"
    READY = "ready"
    ERROR = "error"

@dataclass(frozen=True)
class EmbeddingConfig:
    """Configuration for BGE-M3 embeddings."""
    model_name: str = "BAAI/bge-m3"
    max_length: int = 8192
    batch_size: int = 32
    device: str = "auto"
    normalize_embeddings: bool = True
    trust_remote_code: bool = True

class EmbeddingRequest(BaseModel):
    """Request model for embedding generation."""
    texts: List[str] = Field(..., min_items=1, max_items=100)
    normalize: bool = Field(default=True)

    @field_validator('texts')
    @classmethod
    def validate_texts(cls, v: List[str]) -> List[str]:
        """Validate and sanitize input texts."""
        if not v:
            raise ValueError("Texts list cannot be empty")

        sanitized_texts = []
        for text in v:
            if not isinstance(text, str):
                raise ValueError("All texts must be strings")

            # Sanitize text - remove null bytes and control characters
            sanitized = text.replace('\x00', '').strip()
            if not sanitized:
                raise ValueError("Text cannot be empty after sanitization")

            # Truncate if too long
            if len(sanitized) > 8192:
                sanitized = sanitized[:8192]
                logger.warning(f"Text truncated to {8192} characters", extra={"error_code": "TEXT_TRUNCATED"})

            sanitized_texts.append(sanitized)

        return sanitized_texts

class EmbeddingResponse(BaseModel):
    """Response model for embedding generation."""
    embeddings: List[List[float]]
    dimensions: int
    model_name: str
    processing_time_ms: float

class BGE_M3_EmbeddingService:
    """BGE-M3 Embedding Service with production-ready error handling."""

    def __init__(self, config: Optional[EmbeddingConfig] = None):
        """Initialize the BGE-M3 embedding service."""
        self.config = config or EmbeddingConfig()
        self.model: Optional[SentenceTransformer] = None
        self.status = ModelStatus.NOT_LOADED
        self._lock = asyncio.Lock()

        # Determine device
        if self.config.device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = self.config.device

        logger.info(f"BGE-M3 service initialized with device: {self.device}",
                   extra={"device": self.device, "model_name": self.config.model_name})

    async def load_model(self) -> None:
        """Load the BGE-M3 model asynchronously."""
        async with self._lock:
            if self.status == ModelStatus.READY:
                return

            if self.status == ModelStatus.LOADING:
                # Wait for loading to complete
                while self.status == ModelStatus.LOADING:
                    await asyncio.sleep(0.1)
                return

            try:
                self.status = ModelStatus.LOADING
                logger.info("Loading BGE-M3 model...", extra={"model_name": self.config.model_name})

                # Load model in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                self.model = await loop.run_in_executor(
                    None,
                    self._load_model_sync
                )

                self.status = ModelStatus.READY
                logger.info("BGE-M3 model loaded successfully",
                           extra={"model_name": self.config.model_name, "device": self.device})

            except Exception as e:
                self.status = ModelStatus.ERROR
                error_code = "MODEL_LOAD_FAILED"
                logger.error(f"Failed to load BGE-M3 model",
                           extra={"error_code": error_code, "model_name": self.config.model_name})
                raise EmbeddingError(f"Failed to load model: {str(e)}", error_code)

    def _load_model_sync(self) -> SentenceTransformer:
        """Synchronous model loading."""
        return SentenceTransformer(
            self.config.model_name,
            device=self.device,
            trust_remote_code=self.config.trust_remote_code
        )

    async def generate_embeddings(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """Generate embeddings for the given texts."""
        import time
        start_time = time.time()

        # Ensure model is loaded
        if self.status != ModelStatus.READY:
            await self.load_model()

        if not self.model:
            raise EmbeddingError("Model not available", "MODEL_NOT_READY")

        try:
            # Process in batches for memory efficiency
            all_embeddings = []
            texts = request.texts

            for i in range(0, len(texts), self.config.batch_size):
                batch_texts = texts[i:i + self.config.batch_size]

                # Generate embeddings in thread pool
                loop = asyncio.get_event_loop()
                batch_embeddings = await loop.run_in_executor(
                    None,
                    self._generate_batch_embeddings,
                    batch_texts,
                    request.normalize
                )

                all_embeddings.extend(batch_embeddings)

            processing_time = (time.time() - start_time) * 1000

            # Get embedding dimensions
            dimensions = len(all_embeddings[0]) if all_embeddings else 0

            logger.info(f"Generated embeddings for {len(texts)} texts",
                       extra={
                           "text_count": len(texts),
                           "dimensions": dimensions,
                           "processing_time_ms": processing_time
                       })

            return EmbeddingResponse(
                embeddings=all_embeddings,
                dimensions=dimensions,
                model_name=self.config.model_name,
                processing_time_ms=processing_time
            )

        except Exception as e:
            error_code = "EMBEDDING_GENERATION_FAILED"
            logger.error("Failed to generate embeddings",
                        extra={"error_code": error_code, "text_count": len(request.texts)})
            raise EmbeddingError(f"Embedding generation failed: {str(e)}", error_code)

    def _generate_batch_embeddings(self, texts: List[str], normalize: bool) -> List[List[float]]:
        """Generate embeddings for a batch of texts synchronously."""
        if not self.model:
            raise EmbeddingError("Model not loaded", "MODEL_NOT_READY")

        # Generate embeddings
        embeddings = self.model.encode(
            texts,
            normalize_embeddings=normalize,
            convert_to_numpy=True,
            show_progress_bar=False
        )

        # Convert to list of lists
        return embeddings.tolist()

    async def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this model."""
        if self.status != ModelStatus.READY:
            await self.load_model()

        if not self.model:
            raise EmbeddingError("Model not available", "MODEL_NOT_READY")

        return self.model.get_sentence_embedding_dimension()

    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the embedding service."""
        return {
            "status": self.status.value,
            "model_name": self.config.model_name,
            "device": self.device,
            "max_length": self.config.max_length,
            "batch_size": self.config.batch_size
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the service."""
        try:
            if self.status != ModelStatus.READY:
                await self.load_model()

            # Test with a simple embedding
            test_request = EmbeddingRequest(texts=["Health check test"])
            response = await self.generate_embeddings(test_request)

            return {
                "status": "healthy",
                "model_loaded": True,
                "embedding_dimension": response.dimensions,
                "test_embedding_time_ms": response.processing_time_ms
            }

        except Exception as e:
            logger.error("Health check failed", extra={"error_code": "HEALTH_CHECK_FAILED"})
            return {
                "status": "unhealthy",
                "model_loaded": False,
                "error": str(e)
            }

# Global service instance
_embedding_service: Optional[BGE_M3_EmbeddingService] = None

def get_embedding_service() -> BGE_M3_EmbeddingService:
    """Get the global embedding service instance."""
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = BGE_M3_EmbeddingService()
    return _embedding_service

# Export main classes and functions
__all__ = [
    "BGE_M3_EmbeddingService",
    "EmbeddingRequest",
    "EmbeddingResponse",
    "EmbeddingConfig",
    "EmbeddingError",
    "ModelStatus",
    "get_embedding_service"
]
