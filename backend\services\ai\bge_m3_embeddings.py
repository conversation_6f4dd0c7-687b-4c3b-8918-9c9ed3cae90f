"""
BGE-M3 Embeddings Service

High-performance embeddings service using BGE-M3 model for pharmaceutical compliance documents.
Provides multilingual, multi-granularity embeddings with proper error handling and type safety.

Features:
- BGE-M3 model integration with FlagEmbedding
- Async processing with proper error boundaries
- Type safety with Pydantic validation
- Comprehensive logging and monitoring
- Memory-efficient batch processing
"""

import asyncio
import logging
from typing import List, Optional, Union, Dict, Any
from pathlib import Path
import numpy as np
from pydantic import BaseModel, Field, validator
from FlagEmbedding import BGEM3FlagModel
import torch

# Configure logging
logger = logging.getLogger(__name__)

class EmbeddingRequest(BaseModel):
    """Request model for embedding generation."""
    texts: List[str] = Field(..., min_items=1, max_items=100)
    normalize: bool = Field(default=True)
    batch_size: int = Field(default=32, ge=1, le=64)

    @validator('texts')
    def validate_texts(cls, v):
        """Validate text inputs."""
        if not v:
            raise ValueError("Texts list cannot be empty")
        for text in v:
            if not isinstance(text, str) or len(text.strip()) == 0:
                raise ValueError("All texts must be non-empty strings")
            if len(text) > 8192:  # BGE-M3 max length
                raise ValueError(f"Text length {len(text)} exceeds maximum 8192 characters")
        return v

class EmbeddingResponse(BaseModel):
    """Response model for embedding generation."""
    embeddings: List[List[float]]
    dimensions: int
    model_name: str
    processing_time: float

class EmbeddingError(Exception):
    """Custom exception for embedding service errors."""
    pass

class BGEM3EmbeddingsService:
    """
    BGE-M3 Embeddings Service

    Provides high-performance multilingual embeddings for pharmaceutical compliance documents.
    Supports dense, sparse, and multi-vector representations.
    """

    def __init__(
        self,
        model_name: str = "BAAI/bge-m3",
        device: Optional[str] = None,
        use_fp16: bool = True,
        max_length: int = 8192
    ):
        """
        Initialize BGE-M3 embeddings service.

        Args:
            model_name: HuggingFace model identifier
            device: Device to use ('cuda', 'cpu', or None for auto)
            use_fp16: Whether to use half precision
            max_length: Maximum sequence length
        """
        self.model_name = model_name
        self.max_length = max_length
        self.use_fp16 = use_fp16
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.model: Optional[BGEM3FlagModel] = None
        self._is_initialized = False

        logger.info(f"Initializing BGE-M3 service with device: {self.device}")

    async def initialize(self) -> None:
        """Initialize the BGE-M3 model asynchronously."""
        if self._is_initialized:
            return

        try:
            logger.info(f"Loading BGE-M3 model: {self.model_name}")

            # Load model in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                None,
                self._load_model
            )

            self._is_initialized = True
            logger.info("BGE-M3 model loaded successfully")

        except Exception as e:
            logger.error(f"Failed to initialize BGE-M3 model: {e}")
            raise EmbeddingError(f"Model initialization failed: {e}")

    def _load_model(self) -> BGEM3FlagModel:
        """Load the BGE-M3 model (runs in thread pool)."""
        return BGEM3FlagModel(
            self.model_name,
            use_fp16=self.use_fp16,
            device=self.device
        )

    async def generate_embeddings(
        self,
        request: EmbeddingRequest
    ) -> EmbeddingResponse:
        """
        Generate embeddings for input texts.

        Args:
            request: Embedding request with texts and parameters

        Returns:
            EmbeddingResponse with embeddings and metadata

        Raises:
            EmbeddingError: If embedding generation fails
        """
        if not self._is_initialized:
            await self.initialize()

        if not self.model:
            raise EmbeddingError("Model not initialized")

        try:
            import time
            start_time = time.time()

            # Process texts in batches
            all_embeddings = []

            for i in range(0, len(request.texts), request.batch_size):
                batch_texts = request.texts[i:i + request.batch_size]

                # Generate embeddings in thread pool
                loop = asyncio.get_event_loop()
                batch_embeddings = await loop.run_in_executor(
                    None,
                    self._generate_batch_embeddings,
                    batch_texts,
                    request.normalize
                )

                all_embeddings.extend(batch_embeddings)

            processing_time = time.time() - start_time

            # Get embedding dimensions
            dimensions = len(all_embeddings[0]) if all_embeddings else 0

            logger.info(
                f"Generated {len(all_embeddings)} embeddings "
                f"in {processing_time:.2f}s"
            )

            return EmbeddingResponse(
                embeddings=all_embeddings,
                dimensions=dimensions,
                model_name=self.model_name,
                processing_time=processing_time
            )

        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            raise EmbeddingError(f"Failed to generate embeddings: {e}")

    def _generate_batch_embeddings(
        self,
        texts: List[str],
        normalize: bool
    ) -> List[List[float]]:
        """Generate embeddings for a batch of texts (runs in thread pool)."""
        if not self.model:
            raise EmbeddingError("Model not initialized")

        # Generate dense embeddings
        embeddings = self.model.encode(
            texts,
            batch_size=len(texts),
            max_length=self.max_length,
            return_dense=True,
            return_sparse=False,
            return_colbert_vecs=False
        )['dense_vecs']

        # Normalize if requested
        if normalize:
            embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)

        return embeddings.tolist()

    async def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        if not self._is_initialized:
            await self.initialize()

        return {
            "model_name": self.model_name,
            "device": self.device,
            "max_length": self.max_length,
            "use_fp16": self.use_fp16,
            "is_initialized": self._is_initialized,
            "dimensions": 1024  # BGE-M3 embedding dimension
        }

    async def health_check(self) -> bool:
        """Check if the service is healthy."""
        try:
            if not self._is_initialized:
                await self.initialize()

            # Test with a simple embedding
            test_request = EmbeddingRequest(texts=["test"])
            await self.generate_embeddings(test_request)
            return True

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

# Global service instance
_embeddings_service: Optional[BGEM3EmbeddingsService] = None

async def get_embeddings_service() -> BGEM3EmbeddingsService:
    """Get or create the global embeddings service instance."""
    global _embeddings_service

    if _embeddings_service is None:
        _embeddings_service = BGEM3EmbeddingsService()
        await _embeddings_service.initialize()

    return _embeddings_service
