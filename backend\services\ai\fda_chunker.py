"""FDA Document Chunker Service for VigiLens.

Provides FDA-specific document chunking strategies for optimal RAG performance.
"""

import logging
import re
from typing import Any, Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from pydantic import BaseModel, Field, ConfigDict

logger = logging.getLogger(__name__)


class ChunkingStrategy(Enum):
    """Document chunking strategies."""
    FIXED_SIZE = "fixed_size"
    SEMANTIC = "semantic"
    SECTION_BASED = "section_based"
    HYBRID = "hybrid"
    FDA_SPECIFIC = "fda_specific"


class ChunkType(Enum):
    """Types of document chunks."""
    PARAGRAPH = "paragraph"
    SECTION = "section"
    TABLE = "table"
    LIST = "list"
    HEADER = "header"
    FOOTER = "footer"
    REGULATION = "regulation"
    GUIDANCE = "guidance"
    WARNING = "warning"
    PROCEDURE = "procedure"


class DocumentChunk(BaseModel):
    """A chunk of document content."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    chunk_id: str = Field(description="Unique chunk identifier")
    content: str = Field(description="Chunk text content")
    chunk_type: ChunkType = Field(description="Type of chunk")
    chunk_index: int = Field(description="Index in document")
    start_char: int = Field(description="Start character position")
    end_char: int = Field(description="End character position")
    word_count: int = Field(description="Number of words")
    char_count: int = Field(description="Number of characters")
    page_number: Optional[int] = Field(default=None, description="Source page number")
    section_title: Optional[str] = Field(default=None, description="Section title")
    parent_section: Optional[str] = Field(default=None, description="Parent section")
    regulation_number: Optional[str] = Field(default=None, description="FDA regulation number")
    importance_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Importance score")
    keywords: List[str] = Field(default_factory=list, description="Extracted keywords")
    entities: List[str] = Field(default_factory=list, description="Named entities")
    overlap_prev: int = Field(default=0, description="Overlap with previous chunk")
    overlap_next: int = Field(default=0, description="Overlap with next chunk")


class FDAChunkingConfig(BaseModel):
    """Configuration for FDA document chunking."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    strategy: ChunkingStrategy = Field(default=ChunkingStrategy.FDA_SPECIFIC, description="Chunking strategy")
    max_chunk_size: int = Field(default=1000, ge=100, le=5000, description="Maximum chunk size in characters")
    min_chunk_size: int = Field(default=100, ge=50, le=1000, description="Minimum chunk size in characters")
    overlap_size: int = Field(default=100, ge=0, le=500, description="Overlap between chunks")
    preserve_sections: bool = Field(default=True, description="Preserve section boundaries")
    preserve_paragraphs: bool = Field(default=True, description="Preserve paragraph boundaries")
    extract_tables: bool = Field(default=True, description="Extract tables as separate chunks")
    extract_lists: bool = Field(default=True, description="Extract lists as separate chunks")
    identify_regulations: bool = Field(default=True, description="Identify FDA regulations")
    calculate_importance: bool = Field(default=True, description="Calculate chunk importance")
    extract_keywords: bool = Field(default=True, description="Extract keywords")
    min_words_per_chunk: int = Field(default=10, ge=1, description="Minimum words per chunk")
    split_long_sentences: bool = Field(default=True, description="Split very long sentences")
    max_sentence_length: int = Field(default=300, ge=100, description="Maximum sentence length")


class FDAChunker:
    """FDA-specific document chunker."""
    
    def __init__(self, config: Optional[FDAChunkingConfig] = None):
        """Initialize FDA chunker."""
        self.config = config or FDAChunkingConfig()
        
        # FDA-specific patterns
        self.regulation_patterns = [
            r'\b\d+\s*CFR\s*\d+(?:\.\d+)*\b',  # CFR references
            r'\b21\s*CFR\s*\d+(?:\.\d+)*\b',   # 21 CFR specifically
            r'\bSection\s*\d+(?:\.\d+)*\b',     # Section references
            r'\b\d+\s*USC\s*\d+(?:\.\d+)*\b',  # USC references
        ]
        
        self.section_patterns = [
            r'^\s*\d+(?:\.\d+)*\s+[A-Z][^\n]*$',  # Numbered sections
            r'^\s*[A-Z][^\n]*:?\s*$',              # Title case headers
            r'^\s*[IVX]+\.\s+[A-Z][^\n]*$',        # Roman numeral sections
        ]
        
        self.fda_keywords = [
            'adverse event', 'clinical trial', 'drug approval', 'safety',
            'efficacy', 'labeling', 'manufacturing', 'quality control',
            'pharmacovigilance', 'risk assessment', 'compliance',
            'inspection', 'warning letter', 'recall', 'guidance',
            'regulation', 'submission', 'application', 'review'
        ]
        
    def chunk_document(
        self,
        text: str,
        document_id: str,
        page_number: Optional[int] = None
    ) -> List[DocumentChunk]:
        """Chunk a document using FDA-specific strategy."""
        try:
            if self.config.strategy == ChunkingStrategy.FDA_SPECIFIC:
                return self._chunk_fda_specific(text, document_id, page_number)
            elif self.config.strategy == ChunkingStrategy.SECTION_BASED:
                return self._chunk_section_based(text, document_id, page_number)
            elif self.config.strategy == ChunkingStrategy.SEMANTIC:
                return self._chunk_semantic(text, document_id, page_number)
            elif self.config.strategy == ChunkingStrategy.HYBRID:
                return self._chunk_hybrid(text, document_id, page_number)
            else:
                return self._chunk_fixed_size(text, document_id, page_number)
                
        except Exception as e:
            logger.error(f"Failed to chunk document {document_id}: {e}")
            # Fallback to simple fixed-size chunking
            return self._chunk_fixed_size(text, document_id, page_number)
            
    def _chunk_fda_specific(self, text: str, document_id: str, page_number: Optional[int]) -> List[DocumentChunk]:
        """FDA-specific chunking strategy."""
        chunks = []
        
        # First, identify major sections
        sections = self._identify_sections(text)
        
        chunk_index = 0
        for section_start, section_end, section_title in sections:
            section_text = text[section_start:section_end]
            
            # Process each section
            section_chunks = self._process_section(
                section_text, document_id, chunk_index, 
                section_start, section_title, page_number
            )
            
            chunks.extend(section_chunks)
            chunk_index += len(section_chunks)
            
        # If no sections found, use paragraph-based chunking
        if not chunks:
            chunks = self._chunk_by_paragraphs(text, document_id, page_number)
            
        return chunks
        
    def _identify_sections(self, text: str) -> List[Tuple[int, int, str]]:
        """Identify document sections."""
        sections = []
        lines = text.split('\n')
        
        current_start = 0
        current_title = "Introduction"
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # Check if line matches section pattern
            is_section = False
            for pattern in self.section_patterns:
                if re.match(pattern, line_stripped, re.IGNORECASE):
                    is_section = True
                    break
                    
            if is_section and i > 0:
                # End previous section
                section_end = sum(len(lines[j]) + 1 for j in range(i))
                sections.append((current_start, section_end, current_title))
                
                # Start new section
                current_start = section_end
                current_title = line_stripped[:100]  # Limit title length
                
        # Add final section
        if current_start < len(text):
            sections.append((current_start, len(text), current_title))
            
        return sections
        
    def _process_section(
        self, 
        section_text: str, 
        document_id: str, 
        start_index: int,
        section_start: int,
        section_title: str,
        page_number: Optional[int]
    ) -> List[DocumentChunk]:
        """Process a document section."""
        chunks = []
        
        # Split section into paragraphs
        paragraphs = self._split_paragraphs(section_text)
        
        current_chunk = ""
        current_start = section_start
        chunk_index = start_index
        
        for paragraph in paragraphs:
            # Check if adding paragraph would exceed max size
            if (len(current_chunk) + len(paragraph) > self.config.max_chunk_size and 
                len(current_chunk) >= self.config.min_chunk_size):
                
                # Create chunk from current content
                if current_chunk.strip():
                    chunk = self._create_chunk(
                        current_chunk, document_id, chunk_index,
                        current_start, current_start + len(current_chunk),
                        section_title, page_number
                    )
                    chunks.append(chunk)
                    chunk_index += 1
                    
                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk, self.config.overlap_size)
                current_chunk = overlap_text + paragraph
                current_start += len(current_chunk) - len(overlap_text)
            else:
                current_chunk += paragraph
                
        # Add final chunk
        if current_chunk.strip():
            chunk = self._create_chunk(
                current_chunk, document_id, chunk_index,
                current_start, current_start + len(current_chunk),
                section_title, page_number
            )
            chunks.append(chunk)
            
        return chunks
        
    def _split_paragraphs(self, text: str) -> List[str]:
        """Split text into paragraphs."""
        # Split on double newlines or single newlines followed by significant whitespace
        paragraphs = re.split(r'\n\s*\n|\n(?=\s{4,})', text)
        
        # Clean and filter paragraphs
        cleaned_paragraphs = []
        for para in paragraphs:
            para = para.strip()
            if para and len(para.split()) >= self.config.min_words_per_chunk:
                cleaned_paragraphs.append(para + '\n\n')
                
        return cleaned_paragraphs
        
    def _create_chunk(
        self,
        content: str,
        document_id: str,
        chunk_index: int,
        start_char: int,
        end_char: int,
        section_title: Optional[str],
        page_number: Optional[int]
    ) -> DocumentChunk:
        """Create a document chunk."""
        content = content.strip()
        
        # Determine chunk type
        chunk_type = self._determine_chunk_type(content)
        
        # Extract keywords and entities
        keywords = self._extract_keywords(content) if self.config.extract_keywords else []
        entities = self._extract_entities(content)
        
        # Calculate importance score
        importance_score = self._calculate_importance(content) if self.config.calculate_importance else 0.0
        
        # Extract regulation number if applicable
        regulation_number = self._extract_regulation_number(content) if self.config.identify_regulations else None
        
        chunk_id = f"{document_id}_chunk_{chunk_index:04d}"
        
        return DocumentChunk(
            chunk_id=chunk_id,
            content=content,
            chunk_type=chunk_type,
            chunk_index=chunk_index,
            start_char=start_char,
            end_char=end_char,
            word_count=len(content.split()),
            char_count=len(content),
            page_number=page_number,
            section_title=section_title,
            regulation_number=regulation_number,
            importance_score=importance_score,
            keywords=keywords,
            entities=entities
        )
        
    def _determine_chunk_type(self, content: str) -> ChunkType:
        """Determine the type of chunk based on content."""
        content_lower = content.lower()
        
        # Check for specific FDA content types
        if any(pattern in content_lower for pattern in ['warning', 'caution', 'alert']):
            return ChunkType.WARNING
        elif any(pattern in content_lower for pattern in ['procedure', 'protocol', 'method']):
            return ChunkType.PROCEDURE
        elif any(pattern in content_lower for pattern in ['regulation', 'cfr', 'usc']):
            return ChunkType.REGULATION
        elif any(pattern in content_lower for pattern in ['guidance', 'recommendation']):
            return ChunkType.GUIDANCE
        elif content.count('\t') > 3 or content.count('|') > 3:
            return ChunkType.TABLE
        elif content.count('\n•') > 2 or content.count('\n-') > 2:
            return ChunkType.LIST
        else:
            return ChunkType.PARAGRAPH
            
    def _extract_keywords(self, content: str) -> List[str]:
        """Extract keywords from content."""
        content_lower = content.lower()
        found_keywords = []
        
        for keyword in self.fda_keywords:
            if keyword in content_lower:
                found_keywords.append(keyword)
                
        return found_keywords
        
    def _extract_entities(self, content: str) -> List[str]:
        """Extract named entities (simplified)."""
        entities = []
        
        # Extract drug names (capitalized words)
        drug_pattern = r'\b[A-Z][a-z]+(?:in|ol|ide|ine|ate)\b'
        drugs = re.findall(drug_pattern, content)
        entities.extend(drugs)
        
        # Extract company names (words ending in Inc, Corp, etc.)
        company_pattern = r'\b[A-Z][a-zA-Z\s]+(?:Inc|Corp|LLC|Ltd)\.?\b'
        companies = re.findall(company_pattern, content)
        entities.extend(companies)
        
        return list(set(entities))  # Remove duplicates
        
    def _extract_regulation_number(self, content: str) -> Optional[str]:
        """Extract FDA regulation numbers."""
        for pattern in self.regulation_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(0)
        return None
        
    def _calculate_importance(self, content: str) -> float:
        """Calculate importance score for chunk."""
        score = 0.0
        content_lower = content.lower()
        
        # Higher score for regulatory content
        if any(word in content_lower for word in ['shall', 'must', 'required', 'mandatory']):
            score += 0.3
            
        # Higher score for safety-related content
        if any(word in content_lower for word in ['safety', 'adverse', 'risk', 'warning']):
            score += 0.2
            
        # Higher score for procedure content
        if any(word in content_lower for word in ['procedure', 'protocol', 'method', 'process']):
            score += 0.2
            
        # Higher score for content with regulations
        if any(re.search(pattern, content, re.IGNORECASE) for pattern in self.regulation_patterns):
            score += 0.3
            
        return min(score, 1.0)
        
    def _get_overlap_text(self, text: str, overlap_size: int) -> str:
        """Get overlap text from end of chunk."""
        if len(text) <= overlap_size:
            return text
        return text[-overlap_size:]
        
    def _chunk_fixed_size(self, text: str, document_id: str, page_number: Optional[int]) -> List[DocumentChunk]:
        """Simple fixed-size chunking fallback."""
        chunks = []
        chunk_size = self.config.max_chunk_size
        overlap = self.config.overlap_size
        
        start = 0
        chunk_index = 0
        
        while start < len(text):
            end = min(start + chunk_size, len(text))
            chunk_text = text[start:end]
            
            if chunk_text.strip():
                chunk = self._create_chunk(
                    chunk_text, document_id, chunk_index,
                    start, end, None, page_number
                )
                chunks.append(chunk)
                chunk_index += 1
                
            start = end - overlap
            
        return chunks
        
    def _chunk_section_based(self, text: str, document_id: str, page_number: Optional[int]) -> List[DocumentChunk]:
        """Section-based chunking."""
        return self._chunk_fda_specific(text, document_id, page_number)
        
    def _chunk_semantic(self, text: str, document_id: str, page_number: Optional[int]) -> List[DocumentChunk]:
        """Semantic chunking (simplified)."""
        return self._chunk_by_paragraphs(text, document_id, page_number)
        
    def _chunk_hybrid(self, text: str, document_id: str, page_number: Optional[int]) -> List[DocumentChunk]:
        """Hybrid chunking strategy."""
        return self._chunk_fda_specific(text, document_id, page_number)
        
    def _chunk_by_paragraphs(self, text: str, document_id: str, page_number: Optional[int]) -> List[DocumentChunk]:
        """Chunk by paragraphs."""
        paragraphs = self._split_paragraphs(text)
        chunks = []
        
        current_chunk = ""
        current_start = 0
        chunk_index = 0
        
        for paragraph in paragraphs:
            if (len(current_chunk) + len(paragraph) > self.config.max_chunk_size and 
                len(current_chunk) >= self.config.min_chunk_size):
                
                if current_chunk.strip():
                    chunk = self._create_chunk(
                        current_chunk, document_id, chunk_index,
                        current_start, current_start + len(current_chunk),
                        None, page_number
                    )
                    chunks.append(chunk)
                    chunk_index += 1
                    
                current_chunk = paragraph
                current_start += len(current_chunk)
            else:
                current_chunk += paragraph
                
        if current_chunk.strip():
            chunk = self._create_chunk(
                current_chunk, document_id, chunk_index,
                current_start, current_start + len(current_chunk),
                None, page_number
            )
            chunks.append(chunk)
            
        return chunks


# Global chunker instance
_global_chunker: Optional[FDAChunker] = None


def get_fda_chunker(config: Optional[FDAChunkingConfig] = None) -> FDAChunker:
    """Get FDA chunker instance."""
    global _global_chunker
    
    if _global_chunker is None or config is not None:
        _global_chunker = FDAChunker(config)
        
    return _global_chunker