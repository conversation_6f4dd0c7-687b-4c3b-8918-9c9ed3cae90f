"""FDA Metadata Extraction Service

This module provides FDA-specific metadata extraction capabilities for regulatory documents.
It extracts structured metadata from FDA documents including regulation numbers, sections,
dates, and other regulatory information.
"""

import re
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Set
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, validator

logger = logging.getLogger(__name__)

class MetadataType(str, Enum):
    """Types of metadata that can be extracted"""
    REGULATION = "regulation"
    SECTION = "section"
    SUBSECTION = "subsection"
    PART = "part"
    SUBPART = "subpart"
    DATE = "date"
    AUTHORITY = "authority"
    CITATION = "citation"
    DEFINITION = "definition"
    REQUIREMENT = "requirement"
    PROCEDURE = "procedure"
    GUIDANCE = "guidance"
    WARNING = "warning"
    EXEMPTION = "exemption"
    CLASSIFICATION = "classification"

class ConfidenceLevel(str, Enum):
    """Confidence levels for metadata extraction"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class MetadataItem(BaseModel):
    """Individual metadata item"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    type: MetadataType = Field(..., description="Type of metadata")
    value: str = Field(..., description="Extracted value")
    confidence: ConfidenceLevel = Field(default=ConfidenceLevel.MEDIUM, description="Confidence level")
    context: Optional[str] = Field(None, description="Surrounding context")
    position: Optional[int] = Field(None, description="Position in document")
    source_section: Optional[str] = Field(None, description="Source section identifier")
    
class DocumentMetadata(BaseModel):
    """Complete document metadata"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    document_id: str = Field(..., description="Document identifier")
    title: Optional[str] = Field(None, description="Document title")
    cfr_title: Optional[str] = Field(None, description="CFR Title (e.g., '21')")
    cfr_part: Optional[str] = Field(None, description="CFR Part (e.g., '820')")
    effective_date: Optional[datetime] = Field(None, description="Effective date")
    last_updated: Optional[datetime] = Field(None, description="Last update date")
    authority: List[str] = Field(default_factory=list, description="Regulatory authorities")
    subjects: List[str] = Field(default_factory=list, description="Subject areas")
    metadata_items: List[MetadataItem] = Field(default_factory=list, description="Extracted metadata items")
    extraction_timestamp: datetime = Field(default_factory=datetime.now, description="When metadata was extracted")
    
class FDAMetadataConfig(BaseModel):
    """Configuration for FDA metadata extraction"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    extract_regulations: bool = Field(default=True, description="Extract regulation numbers")
    extract_sections: bool = Field(default=True, description="Extract section references")
    extract_dates: bool = Field(default=True, description="Extract dates")
    extract_authorities: bool = Field(default=True, description="Extract authorities")
    extract_definitions: bool = Field(default=True, description="Extract definitions")
    extract_requirements: bool = Field(default=True, description="Extract requirements")
    min_confidence: ConfidenceLevel = Field(default=ConfidenceLevel.LOW, description="Minimum confidence level")
    max_context_length: int = Field(default=200, description="Maximum context length")
    
class FDAMetadataExtractor:
    """FDA-specific metadata extraction service"""
    
    def __init__(self, config: Optional[FDAMetadataConfig] = None):
        """Initialize the metadata extractor
        
        Args:
            config: Configuration for metadata extraction
        """
        self.config = config or FDAMetadataConfig()
        self._setup_patterns()
        
    def _setup_patterns(self) -> None:
        """Setup regex patterns for metadata extraction"""
        # CFR patterns
        self.cfr_patterns = {
            'full_citation': re.compile(r'\b(\d+)\s+CFR\s+(\d+(?:\.\d+)*)', re.IGNORECASE),
            'section': re.compile(r'§\s*(\d+(?:\.\d+)*)', re.IGNORECASE),
            'part': re.compile(r'\bPart\s+(\d+)', re.IGNORECASE),
            'subpart': re.compile(r'\bSubpart\s+([A-Z])', re.IGNORECASE)
        }
        
        # Date patterns
        self.date_patterns = {
            'effective_date': re.compile(r'effective\s+(?:date\s+)?(?:is\s+)?([A-Za-z]+\s+\d{1,2},\s+\d{4})', re.IGNORECASE),
            'published_date': re.compile(r'published\s+(?:on\s+)?([A-Za-z]+\s+\d{1,2},\s+\d{4})', re.IGNORECASE),
            'revised_date': re.compile(r'revised\s+(?:on\s+)?([A-Za-z]+\s+\d{1,2},\s+\d{4})', re.IGNORECASE)
        }
        
        # Authority patterns
        self.authority_patterns = {
            'fda': re.compile(r'\b(?:FDA|Food and Drug Administration)\b', re.IGNORECASE),
            'hhs': re.compile(r'\b(?:HHS|Department of Health and Human Services)\b', re.IGNORECASE),
            'cdrh': re.compile(r'\b(?:CDRH|Center for Devices and Radiological Health)\b', re.IGNORECASE),
            'cber': re.compile(r'\b(?:CBER|Center for Biologics Evaluation and Research)\b', re.IGNORECASE)
        }
        
        # Definition patterns
        self.definition_patterns = {
            'means': re.compile(r'"([^"]+)"\s+means\s+([^.]+)', re.IGNORECASE),
            'defined_as': re.compile(r'([A-Za-z\s]+)\s+is\s+defined\s+as\s+([^.]+)', re.IGNORECASE)
        }
        
        # Requirement patterns
        self.requirement_patterns = {
            'shall': re.compile(r'([^.]+\bshall\b[^.]+)', re.IGNORECASE),
            'must': re.compile(r'([^.]+\bmust\b[^.]+)', re.IGNORECASE),
            'required': re.compile(r'([^.]+\brequired\b[^.]+)', re.IGNORECASE)
        }
        
    def extract_metadata(self, text: str, document_id: str) -> DocumentMetadata:
        """Extract metadata from FDA document text
        
        Args:
            text: Document text to analyze
            document_id: Unique document identifier
            
        Returns:
            DocumentMetadata: Extracted metadata
        """
        try:
            metadata = DocumentMetadata(document_id=document_id)
            
            # Extract basic document info
            metadata.title = self._extract_title(text)
            metadata.cfr_title, metadata.cfr_part = self._extract_cfr_info(text)
            
            # Extract metadata items
            if self.config.extract_regulations:
                metadata.metadata_items.extend(self._extract_regulations(text))
                
            if self.config.extract_sections:
                metadata.metadata_items.extend(self._extract_sections(text))
                
            if self.config.extract_dates:
                dates = self._extract_dates(text)
                metadata.metadata_items.extend(dates)
                # Set specific date fields
                for date_item in dates:
                    if 'effective' in date_item.context.lower() if date_item.context else False:
                        try:
                            metadata.effective_date = datetime.strptime(date_item.value, '%B %d, %Y')
                        except ValueError:
                            pass
                            
            if self.config.extract_authorities:
                authorities = self._extract_authorities(text)
                metadata.metadata_items.extend(authorities)
                metadata.authority = [item.value for item in authorities]
                
            if self.config.extract_definitions:
                metadata.metadata_items.extend(self._extract_definitions(text))
                
            if self.config.extract_requirements:
                metadata.metadata_items.extend(self._extract_requirements(text))
                
            # Filter by confidence level
            metadata.metadata_items = [
                item for item in metadata.metadata_items
                if self._confidence_level_value(item.confidence) >= self._confidence_level_value(self.config.min_confidence)
            ]
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata from document {document_id}: {e}")
            return DocumentMetadata(document_id=document_id)
            
    def _extract_title(self, text: str) -> Optional[str]:
        """Extract document title"""
        lines = text.split('\n')[:10]  # Check first 10 lines
        for line in lines:
            line = line.strip()
            if len(line) > 10 and len(line) < 200 and not line.startswith('§'):
                if any(word in line.upper() for word in ['CFR', 'PART', 'TITLE']):
                    return line
        return None
        
    def _extract_cfr_info(self, text: str) -> tuple[Optional[str], Optional[str]]:
        """Extract CFR title and part information"""
        cfr_match = self.cfr_patterns['full_citation'].search(text)
        if cfr_match:
            return cfr_match.group(1), cfr_match.group(2).split('.')[0]
            
        part_match = self.cfr_patterns['part'].search(text)
        if part_match:
            return "21", part_match.group(1)  # Default to Title 21 for FDA
            
        return None, None
        
    def _extract_regulations(self, text: str) -> List[MetadataItem]:
        """Extract regulation references"""
        items = []
        
        for pattern_name, pattern in self.cfr_patterns.items():
            for match in pattern.finditer(text):
                context = self._get_context(text, match.start(), match.end())
                confidence = self._determine_confidence(match.group(), context)
                
                items.append(MetadataItem(
                    type=MetadataType.REGULATION,
                    value=match.group(),
                    confidence=confidence,
                    context=context,
                    position=match.start()
                ))
                
        return items
        
    def _extract_sections(self, text: str) -> List[MetadataItem]:
        """Extract section references"""
        items = []
        
        for match in self.cfr_patterns['section'].finditer(text):
            context = self._get_context(text, match.start(), match.end())
            confidence = ConfidenceLevel.HIGH  # Section markers are usually reliable
            
            items.append(MetadataItem(
                type=MetadataType.SECTION,
                value=match.group(1),
                confidence=confidence,
                context=context,
                position=match.start()
            ))
            
        return items
        
    def _extract_dates(self, text: str) -> List[MetadataItem]:
        """Extract dates from text"""
        items = []
        
        for date_type, pattern in self.date_patterns.items():
            for match in pattern.finditer(text):
                context = self._get_context(text, match.start(), match.end())
                confidence = ConfidenceLevel.HIGH
                
                items.append(MetadataItem(
                    type=MetadataType.DATE,
                    value=match.group(1),
                    confidence=confidence,
                    context=context,
                    position=match.start()
                ))
                
        return items
        
    def _extract_authorities(self, text: str) -> List[MetadataItem]:
        """Extract regulatory authorities"""
        items = []
        found_authorities = set()
        
        for authority_name, pattern in self.authority_patterns.items():
            for match in pattern.finditer(text):
                if match.group() not in found_authorities:
                    found_authorities.add(match.group())
                    context = self._get_context(text, match.start(), match.end())
                    
                    items.append(MetadataItem(
                        type=MetadataType.AUTHORITY,
                        value=match.group(),
                        confidence=ConfidenceLevel.HIGH,
                        context=context,
                        position=match.start()
                    ))
                    
        return items
        
    def _extract_definitions(self, text: str) -> List[MetadataItem]:
        """Extract definitions"""
        items = []
        
        for def_type, pattern in self.definition_patterns.items():
            for match in pattern.finditer(text):
                context = self._get_context(text, match.start(), match.end())
                confidence = ConfidenceLevel.MEDIUM
                
                items.append(MetadataItem(
                    type=MetadataType.DEFINITION,
                    value=f"{match.group(1)}: {match.group(2)}",
                    confidence=confidence,
                    context=context,
                    position=match.start()
                ))
                
        return items
        
    def _extract_requirements(self, text: str) -> List[MetadataItem]:
        """Extract requirements"""
        items = []
        
        for req_type, pattern in self.requirement_patterns.items():
            for match in pattern.finditer(text):
                requirement_text = match.group(1).strip()
                if len(requirement_text) > 20:  # Filter out very short matches
                    context = self._get_context(text, match.start(), match.end())
                    confidence = ConfidenceLevel.MEDIUM
                    
                    items.append(MetadataItem(
                        type=MetadataType.REQUIREMENT,
                        value=requirement_text,
                        confidence=confidence,
                        context=context,
                        position=match.start()
                    ))
                    
        return items
        
    def _get_context(self, text: str, start: int, end: int) -> str:
        """Get surrounding context for a match"""
        context_start = max(0, start - self.config.max_context_length // 2)
        context_end = min(len(text), end + self.config.max_context_length // 2)
        return text[context_start:context_end].strip()
        
    def _determine_confidence(self, match_text: str, context: str) -> ConfidenceLevel:
        """Determine confidence level for a match"""
        # Simple heuristics for confidence
        if len(match_text) < 5:
            return ConfidenceLevel.LOW
        elif any(keyword in context.lower() for keyword in ['cfr', 'section', 'part']):
            return ConfidenceLevel.HIGH
        else:
            return ConfidenceLevel.MEDIUM
            
    def _confidence_level_value(self, level: ConfidenceLevel) -> int:
        """Convert confidence level to numeric value for comparison"""
        return {'low': 1, 'medium': 2, 'high': 3}[level.value]
        
    def get_metadata_summary(self, metadata: DocumentMetadata) -> Dict[str, Any]:
        """Get a summary of extracted metadata
        
        Args:
            metadata: Document metadata
            
        Returns:
            Dict containing metadata summary
        """
        summary = {
            'document_id': metadata.document_id,
            'title': metadata.title,
            'cfr_reference': f"{metadata.cfr_title} CFR {metadata.cfr_part}" if metadata.cfr_title and metadata.cfr_part else None,
            'total_items': len(metadata.metadata_items),
            'items_by_type': {},
            'confidence_distribution': {},
            'authorities': metadata.authority,
            'effective_date': metadata.effective_date.isoformat() if metadata.effective_date else None
        }
        
        # Count items by type
        for item in metadata.metadata_items:
            item_type = item.type.value
            summary['items_by_type'][item_type] = summary['items_by_type'].get(item_type, 0) + 1
            
        # Count by confidence
        for item in metadata.metadata_items:
            confidence = item.confidence.value
            summary['confidence_distribution'][confidence] = summary['confidence_distribution'].get(confidence, 0) + 1
            
        return summary
        
    def filter_metadata(self, metadata: DocumentMetadata, 
                       types: Optional[List[MetadataType]] = None,
                       min_confidence: Optional[ConfidenceLevel] = None) -> List[MetadataItem]:
        """Filter metadata items by type and confidence
        
        Args:
            metadata: Document metadata
            types: Types to include (None for all)
            min_confidence: Minimum confidence level
            
        Returns:
            List of filtered metadata items
        """
        items = metadata.metadata_items
        
        if types:
            items = [item for item in items if item.type in types]
            
        if min_confidence:
            min_value = self._confidence_level_value(min_confidence)
            items = [item for item in items if self._confidence_level_value(item.confidence) >= min_value]
            
        return items

# Global instance
_fda_metadata_extractor: Optional[FDAMetadataExtractor] = None

def get_fda_metadata_extractor(config: Optional[FDAMetadataConfig] = None) -> FDAMetadataExtractor:
    """Get or create the global FDA metadata extractor instance
    
    Args:
        config: Optional configuration for the extractor
        
    Returns:
        FDAMetadataExtractor instance
    """
    global _fda_metadata_extractor
    
    if _fda_metadata_extractor is None or config is not None:
        _fda_metadata_extractor = FDAMetadataExtractor(config)
        
    return _fda_metadata_extractor

# Convenience functions
def extract_document_metadata(text: str, document_id: str, 
                            config: Optional[FDAMetadataConfig] = None) -> DocumentMetadata:
    """Extract metadata from FDA document text
    
    Args:
        text: Document text
        document_id: Document identifier
        config: Optional extraction configuration
        
    Returns:
        DocumentMetadata: Extracted metadata
    """
    extractor = get_fda_metadata_extractor(config)
    return extractor.extract_metadata(text, document_id)

def get_metadata_summary(metadata: DocumentMetadata) -> Dict[str, Any]:
    """Get metadata summary
    
    Args:
        metadata: Document metadata
        
    Returns:
        Dict containing summary information
    """
    extractor = get_fda_metadata_extractor()
    return extractor.get_metadata_summary(metadata)