"""Vector Store Service for VigiLens.

Provides unified interface for vector database operations using Qdrant.
"""

import logging
import os
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from pydantic import BaseModel, Field, ConfigDict

# Import existing Qdrant store
try:
    from .qdrant_store import QdrantVectorStore, QdrantConfig
except ImportError:
    from services.ai.qdrant_store import QdrantVectorStore, QdrantConfig

logger = logging.getLogger(__name__)


class VectorStoreConfig(BaseModel):
    """Configuration for vector store."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    collection_name: str = Field(default="fda_knowledge_base", description="Collection name")
    vector_size: int = Field(default=1024, ge=1, description="Vector dimension size")
    distance_metric: str = Field(default="cosine", description="Distance metric")
    storage_path: str = Field(default="./data/qdrant", description="Storage path")
    host: str = Field(default="localhost", description="Qdrant host")
    port: int = Field(default=6333, ge=1, le=65535, description="Qdrant port")
    timeout: int = Field(default=30, ge=1, le=300, description="Request timeout")
    use_memory: bool = Field(default=True, description="Use in-memory storage")


class DocumentMetadata(BaseModel):
    """Document metadata for vector storage."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    document_id: str = Field(description="Unique document identifier")
    title: Optional[str] = Field(default=None, description="Document title")
    source: Optional[str] = Field(default=None, description="Document source")
    document_type: Optional[str] = Field(default=None, description="Document type")
    chunk_index: Optional[int] = Field(default=None, description="Chunk index")
    total_chunks: Optional[int] = Field(default=None, description="Total chunks")
    created_at: Optional[str] = Field(default=None, description="Creation timestamp")
    file_path: Optional[str] = Field(default=None, description="Original file path")
    page_number: Optional[int] = Field(default=None, description="Page number")
    section: Optional[str] = Field(default=None, description="Document section")
    tags: Optional[List[str]] = Field(default_factory=list, description="Document tags")


class VectorSearchResult(BaseModel):
    """Vector search result."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    content: str = Field(description="Document content")
    metadata: DocumentMetadata = Field(description="Document metadata")
    score: float = Field(description="Similarity score")
    vector_id: Optional[str] = Field(default=None, description="Vector ID")


class VectorStore:
    """Unified vector store interface."""
    
    def __init__(self, config: Optional[VectorStoreConfig] = None):
        """Initialize vector store."""
        self.config = config or VectorStoreConfig()
        self._qdrant_store: Optional[QdrantVectorStore] = None
        self._initialized = False
        
    async def initialize(self) -> bool:
        """Initialize the vector store."""
        try:
            # Create Qdrant configuration
            qdrant_config = QdrantConfig(
                collection_name=self.config.collection_name,
                vector_size=self.config.vector_size,
                distance_metric=self.config.distance_metric,
                storage_path=self.config.storage_path,
                host=self.config.host,
                port=self.config.port,
                timeout=self.config.timeout,
                use_memory=self.config.use_memory
            )
            
            # Initialize Qdrant store
            self._qdrant_store = QdrantVectorStore(qdrant_config)
            await self._qdrant_store.initialize()
            
            self._initialized = True
            logger.info(f"Vector store initialized: {self.config.collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {e}")
            return False
            
    async def add_documents(
        self,
        documents: List[str],
        vectors: List[List[float]],
        metadata: List[DocumentMetadata],
        document_ids: Optional[List[str]] = None
    ) -> bool:
        """Add documents to vector store."""
        if not self._initialized or not self._qdrant_store:
            raise RuntimeError("Vector store not initialized")
            
        try:
            # Convert metadata to dict format
            metadata_dicts = [meta.model_dump() for meta in metadata]
            
            # Use Qdrant store to add documents
            success = await self._qdrant_store.add_documents(
                documents=documents,
                vectors=vectors,
                metadata=metadata_dicts,
                document_ids=document_ids
            )
            
            if success:
                logger.info(f"Added {len(documents)} documents to vector store")
            return success
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            return False
            
    async def search(
        self,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.0,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> List[VectorSearchResult]:
        """Search for similar documents."""
        if not self._initialized or not self._qdrant_store:
            raise RuntimeError("Vector store not initialized")
            
        try:
            # Search using Qdrant store
            results = await self._qdrant_store.search(
                query_vector=query_vector,
                limit=limit,
                score_threshold=score_threshold,
                filter_conditions=filter_conditions
            )
            
            # Convert results to VectorSearchResult format
            search_results = []
            for result in results:
                metadata = DocumentMetadata(**result.get('metadata', {}))
                search_result = VectorSearchResult(
                    content=result.get('content', ''),
                    metadata=metadata,
                    score=result.get('score', 0.0),
                    vector_id=result.get('id')
                )
                search_results.append(search_result)
                
            return search_results
            
        except Exception as e:
            logger.error(f"Failed to search vectors: {e}")
            return []
            
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """Delete documents from vector store."""
        if not self._initialized or not self._qdrant_store:
            raise RuntimeError("Vector store not initialized")
            
        try:
            success = await self._qdrant_store.delete_documents(document_ids)
            if success:
                logger.info(f"Deleted {len(document_ids)} documents from vector store")
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            return False
            
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get collection information."""
        if not self._initialized or not self._qdrant_store:
            raise RuntimeError("Vector store not initialized")
            
        try:
            return await self._qdrant_store.get_collection_info()
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {}
            
    async def close(self):
        """Close vector store connection."""
        if self._qdrant_store:
            await self._qdrant_store.close()
        self._initialized = False


# Global vector store instance
_global_vector_store: Optional[VectorStore] = None


async def get_vector_store() -> VectorStore:
    """Get global vector store instance."""
    global _global_vector_store
    
    if _global_vector_store is None:
        config = VectorStoreConfig(
            collection_name=os.getenv("QDRANT_COLLECTION", "fda_knowledge_base"),
            vector_size=int(os.getenv("VECTOR_SIZE", "1024")),
            storage_path=os.getenv("QDRANT_PATH", "./data/qdrant"),
            host=os.getenv("QDRANT_HOST", "localhost"),
            port=int(os.getenv("QDRANT_PORT", "6333")),
            timeout=int(os.getenv("QDRANT_TIMEOUT", "30")),
            use_memory=os.getenv("QDRANT_USE_MEMORY", "true").lower() == "true"
        )
        
        _global_vector_store = VectorStore(config)
        await _global_vector_store.initialize()
        
    return _global_vector_store


async def get_store() -> VectorStore:
    """Alias for get_vector_store."""
    return await get_vector_store()


async def add_fda_documents(
    documents: List[str],
    vectors: List[List[float]],
    metadata: List[Dict[str, Any]],
    document_ids: Optional[List[str]] = None
) -> bool:
    """Add FDA documents to vector store."""
    try:
        vector_store = await get_vector_store()
        
        # Convert metadata dicts to DocumentMetadata objects
        doc_metadata = []
        for meta in metadata:
            doc_meta = DocumentMetadata(
                document_id=meta.get('document_id', ''),
                title=meta.get('title'),
                source=meta.get('source', 'FDA'),
                document_type=meta.get('document_type', 'FDA_DOCUMENT'),
                chunk_index=meta.get('chunk_index'),
                total_chunks=meta.get('total_chunks'),
                created_at=meta.get('created_at'),
                file_path=meta.get('file_path'),
                page_number=meta.get('page_number'),
                section=meta.get('section'),
                tags=meta.get('tags', [])
            )
            doc_metadata.append(doc_meta)
            
        return await vector_store.add_documents(
            documents=documents,
            vectors=vectors,
            metadata=doc_metadata,
            document_ids=document_ids
        )
        
    except Exception as e:
        logger.error(f"Failed to add FDA documents: {e}")
        return False