"""LangGraph Workflow Orchestration for Pharmaceutical Compliance

Implements sophisticated workflow orchestration using LangGraph for coordinating
RAG pipeline, CrewAI agents, and document processing with state management.
"""

import asyncio
import logging
import time
from typing import List, Optional, Dict, Any, Union, TypedDict, Annotated
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import json
import uuid

from pydantic import BaseModel, Field, field_validator, ConfigDict
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolExecutor

# Import our services
from .rag_pipeline import (
    get_rag_pipeline, DocumentInput, QueryRequest, ProcessingResult, RAGError
)
from .crewai_agents import (
    get_agent_system, DocumentAnalysisRequest, ComplianceCheckRequest,
    AgentResponse, AgentError, TaskPriority
)

# Configure structured logging
logger = logging.getLogger(__name__)

class WorkflowError(Exception):
    """Custom exception for workflow operations."""
    def __init__(self, message: str, error_code: str, workflow_id: Optional[str] = None):
        self.message = message
        self.error_code = error_code
        self.workflow_id = workflow_id
        super().__init__(self.message)

class WorkflowStatus(str, Enum):
    """Workflow status enumeration."""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class WorkflowType(str, Enum):
    """Workflow type enumeration."""
    DOCUMENT_PROCESSING = "document_processing"
    COMPLIANCE_ANALYSIS = "compliance_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    REGULATORY_REVIEW = "regulatory_review"
    BATCH_PROCESSING = "batch_processing"

@dataclass(frozen=True)
class WorkflowConfig:
    """Configuration for workflow orchestration."""
    max_execution_time: int = 1800  # 30 minutes
    max_retries: int = 3
    retry_delay: float = 5.0
    enable_checkpoints: bool = True
    parallel_processing: bool = True
    max_concurrent_tasks: int = 5

class WorkflowState(TypedDict):
    """State structure for LangGraph workflows."""
    workflow_id: str
    workflow_type: str
    status: str
    input_data: Dict[str, Any]
    processed_documents: List[Dict[str, Any]]
    analysis_results: List[Dict[str, Any]]
    compliance_results: List[Dict[str, Any]]
    final_output: Dict[str, Any]
    errors: List[str]
    warnings: List[str]
    execution_metadata: Dict[str, Any]
    current_step: str
    step_history: List[str]
    retry_count: int
    start_time: float
    end_time: Optional[float]

class DocumentProcessingRequest(BaseModel):
    """Request for document processing workflow."""
    documents: List[DocumentInput] = Field(..., description="Documents to process")
    workflow_type: WorkflowType = Field(default=WorkflowType.DOCUMENT_PROCESSING)
    analysis_types: List[str] = Field(default_factory=list, description="Types of analysis to perform")
    compliance_frameworks: List[str] = Field(default_factory=list, description="Compliance frameworks to check")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    enable_parallel: bool = Field(default=True, description="Enable parallel processing")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

    @field_validator('documents')
    @classmethod
    def validate_documents(cls, v: List[DocumentInput]) -> List[DocumentInput]:
        """Validate document list."""
        if not v:
            raise ValueError("At least one document is required")
        if len(v) > 50:  # Reasonable batch limit
            raise ValueError("Too many documents (max 50 per batch)")
        return v

class WorkflowResult(BaseModel):
    """Result from workflow execution."""
    workflow_id: str
    workflow_type: str
    status: str
    documents_processed: int
    analysis_results: List[Dict[str, Any]]
    compliance_results: List[Dict[str, Any]]
    execution_time_ms: float
    success_rate: float
    errors: List[str]
    warnings: List[str]
    final_output: Dict[str, Any]
    metadata: Dict[str, Any]

class PharmaceuticalWorkflowOrchestrator:
    """LangGraph-based workflow orchestrator for pharmaceutical compliance."""

    def __init__(self, config: Optional[WorkflowConfig] = None):
        """Initialize the workflow orchestrator."""
        self.config = config or WorkflowConfig()
        self.rag_pipeline = get_rag_pipeline()
        self.agent_system = get_agent_system()
        self.active_workflows: Dict[str, WorkflowState] = {}
        self.memory_saver = MemorySaver() if self.config.enable_checkpoints else None
        self._lock = asyncio.Lock()

        # Create workflow graphs
        self.workflows = {
            WorkflowType.DOCUMENT_PROCESSING: self._create_document_processing_workflow(),
            WorkflowType.COMPLIANCE_ANALYSIS: self._create_compliance_analysis_workflow(),
            WorkflowType.RISK_ASSESSMENT: self._create_risk_assessment_workflow(),
            WorkflowType.REGULATORY_REVIEW: self._create_regulatory_review_workflow(),
            WorkflowType.BATCH_PROCESSING: self._create_batch_processing_workflow()
        }

        logger.info("Workflow orchestrator initialized",
                   extra={"workflows_count": len(self.workflows)})

    def _create_document_processing_workflow(self) -> StateGraph:
        """Create document processing workflow."""
        workflow = StateGraph(WorkflowState)

        # Define workflow nodes
        workflow.add_node("initialize", self._initialize_workflow)
        workflow.add_node("validate_input", self._validate_input)
        workflow.add_node("process_documents", self._process_documents)
        workflow.add_node("generate_embeddings", self._generate_embeddings)
        workflow.add_node("store_vectors", self._store_vectors)
        workflow.add_node("analyze_content", self._analyze_content)
        workflow.add_node("generate_summary", self._generate_summary)
        workflow.add_node("finalize_results", self._finalize_results)
        workflow.add_node("handle_error", self._handle_error)

        # Define workflow edges
        workflow.set_entry_point("initialize")
        workflow.add_edge("initialize", "validate_input")
        workflow.add_edge("validate_input", "process_documents")
        workflow.add_edge("process_documents", "generate_embeddings")
        workflow.add_edge("generate_embeddings", "store_vectors")
        workflow.add_edge("store_vectors", "analyze_content")
        workflow.add_edge("analyze_content", "generate_summary")
        workflow.add_edge("generate_summary", "finalize_results")
        workflow.add_edge("finalize_results", END)
        workflow.add_edge("handle_error", END)

        # Add conditional edges for error handling
        workflow.add_conditional_edges(
            "validate_input",
            self._should_continue_or_error,
            {"continue": "process_documents", "error": "handle_error"}
        )

        return workflow.compile(checkpointer=self.memory_saver)

    def _create_compliance_analysis_workflow(self) -> StateGraph:
        """Create compliance analysis workflow."""
        workflow = StateGraph(WorkflowState)

        workflow.add_node("initialize", self._initialize_workflow)
        workflow.add_node("validate_input", self._validate_input)
        workflow.add_node("extract_content", self._extract_content)
        workflow.add_node("compliance_check", self._compliance_check)
        workflow.add_node("risk_assessment", self._risk_assessment)
        workflow.add_node("regulatory_review", self._regulatory_review)
        workflow.add_node("consolidate_findings", self._consolidate_findings)
        workflow.add_node("generate_report", self._generate_compliance_report)
        workflow.add_node("finalize_results", self._finalize_results)
        workflow.add_node("handle_error", self._handle_error)

        workflow.set_entry_point("initialize")
        workflow.add_edge("initialize", "validate_input")
        workflow.add_edge("validate_input", "extract_content")
        workflow.add_edge("extract_content", "compliance_check")
        workflow.add_edge("compliance_check", "risk_assessment")
        workflow.add_edge("risk_assessment", "regulatory_review")
        workflow.add_edge("regulatory_review", "consolidate_findings")
        workflow.add_edge("consolidate_findings", "generate_report")
        workflow.add_edge("generate_report", "finalize_results")
        workflow.add_edge("finalize_results", END)
        workflow.add_edge("handle_error", END)

        return workflow.compile(checkpointer=self.memory_saver)

    def _create_risk_assessment_workflow(self) -> StateGraph:
        """Create risk assessment workflow."""
        workflow = StateGraph(WorkflowState)

        workflow.add_node("initialize", self._initialize_workflow)
        workflow.add_node("validate_input", self._validate_input)
        workflow.add_node("identify_risks", self._identify_risks)
        workflow.add_node("assess_severity", self._assess_severity)
        workflow.add_node("evaluate_impact", self._evaluate_impact)
        workflow.add_node("recommend_mitigations", self._recommend_mitigations)
        workflow.add_node("generate_risk_report", self._generate_risk_report)
        workflow.add_node("finalize_results", self._finalize_results)
        workflow.add_node("handle_error", self._handle_error)

        workflow.set_entry_point("initialize")
        workflow.add_edge("initialize", "validate_input")
        workflow.add_edge("validate_input", "identify_risks")
        workflow.add_edge("identify_risks", "assess_severity")
        workflow.add_edge("assess_severity", "evaluate_impact")
        workflow.add_edge("evaluate_impact", "recommend_mitigations")
        workflow.add_edge("recommend_mitigations", "generate_risk_report")
        workflow.add_edge("generate_risk_report", "finalize_results")
        workflow.add_edge("finalize_results", END)
        workflow.add_edge("handle_error", END)

        return workflow.compile(checkpointer=self.memory_saver)

    def _create_regulatory_review_workflow(self) -> StateGraph:
        """Create regulatory review workflow."""
        workflow = StateGraph(WorkflowState)

        workflow.add_node("initialize", self._initialize_workflow)
        workflow.add_node("validate_input", self._validate_input)
        workflow.add_node("regulatory_analysis", self._regulatory_analysis)
        workflow.add_node("guideline_compliance", self._guideline_compliance)
        workflow.add_node("submission_readiness", self._submission_readiness)
        workflow.add_node("generate_regulatory_report", self._generate_regulatory_report)
        workflow.add_node("finalize_results", self._finalize_results)
        workflow.add_node("handle_error", self._handle_error)

        workflow.set_entry_point("initialize")
        workflow.add_edge("initialize", "validate_input")
        workflow.add_edge("validate_input", "regulatory_analysis")
        workflow.add_edge("regulatory_analysis", "guideline_compliance")
        workflow.add_edge("guideline_compliance", "submission_readiness")
        workflow.add_edge("submission_readiness", "generate_regulatory_report")
        workflow.add_edge("generate_regulatory_report", "finalize_results")
        workflow.add_edge("finalize_results", END)
        workflow.add_edge("handle_error", END)

        return workflow.compile(checkpointer=self.memory_saver)

    def _create_batch_processing_workflow(self) -> StateGraph:
        """Create batch processing workflow."""
        workflow = StateGraph(WorkflowState)

        workflow.add_node("initialize", self._initialize_workflow)
        workflow.add_node("validate_input", self._validate_input)
        workflow.add_node("batch_process_documents", self._batch_process_documents)
        workflow.add_node("parallel_analysis", self._parallel_analysis)
        workflow.add_node("aggregate_results", self._aggregate_results)
        workflow.add_node("generate_batch_report", self._generate_batch_report)
        workflow.add_node("finalize_results", self._finalize_results)
        workflow.add_node("handle_error", self._handle_error)

        workflow.set_entry_point("initialize")
        workflow.add_edge("initialize", "validate_input")
        workflow.add_edge("validate_input", "batch_process_documents")
        workflow.add_edge("batch_process_documents", "parallel_analysis")
        workflow.add_edge("parallel_analysis", "aggregate_results")
        workflow.add_edge("aggregate_results", "generate_batch_report")
        workflow.add_edge("generate_batch_report", "finalize_results")
        workflow.add_edge("finalize_results", END)
        workflow.add_edge("handle_error", END)

        return workflow.compile(checkpointer=self.memory_saver)

    async def execute_workflow(self, request: DocumentProcessingRequest) -> WorkflowResult:
        """Execute a workflow based on the request."""
        workflow_id = str(uuid.uuid4())
        start_time = time.time()

        try:
            # Initialize workflow state
            initial_state = WorkflowState(
                workflow_id=workflow_id,
                workflow_type=request.workflow_type.value,
                status=WorkflowStatus.RUNNING.value,
                input_data={
                    "documents": [doc.dict() for doc in request.documents],
                    "analysis_types": request.analysis_types,
                    "compliance_frameworks": request.compliance_frameworks,
                    "priority": request.priority.value,
                    "enable_parallel": request.enable_parallel,
                    "metadata": request.metadata
                },
                processed_documents=[],
                analysis_results=[],
                compliance_results=[],
                final_output={},
                errors=[],
                warnings=[],
                execution_metadata={},
                current_step="initialize",
                step_history=[],
                retry_count=0,
                start_time=start_time,
                end_time=None
            )

            # Store active workflow
            async with self._lock:
                self.active_workflows[workflow_id] = initial_state

            # Get appropriate workflow
            workflow_graph = self.workflows.get(request.workflow_type)
            if not workflow_graph:
                raise WorkflowError(
                    f"Workflow type {request.workflow_type} not supported",
                    "UNSUPPORTED_WORKFLOW_TYPE",
                    workflow_id
                )

            # Execute workflow
            logger.info(f"Starting workflow {workflow_id}",
                       extra={
                           "workflow_id": workflow_id,
                           "workflow_type": request.workflow_type.value,
                           "documents_count": len(request.documents)
                       })

            # Run the workflow
            config = {"configurable": {"thread_id": workflow_id}} if self.memory_saver else {}
            final_state = await workflow_graph.ainvoke(initial_state, config=config)

            # Calculate execution time
            execution_time = (time.time() - start_time) * 1000

            # Calculate success rate
            total_tasks = len(request.documents) * (len(request.analysis_types) or 1)
            successful_tasks = len(final_state.get("analysis_results", []))
            success_rate = (successful_tasks / total_tasks) if total_tasks > 0 else 0.0

            # Create result
            result = WorkflowResult(
                workflow_id=workflow_id,
                workflow_type=request.workflow_type.value,
                status=final_state.get("status", WorkflowStatus.COMPLETED.value),
                documents_processed=len(final_state.get("processed_documents", [])),
                analysis_results=final_state.get("analysis_results", []),
                compliance_results=final_state.get("compliance_results", []),
                execution_time_ms=execution_time,
                success_rate=success_rate,
                errors=final_state.get("errors", []),
                warnings=final_state.get("warnings", []),
                final_output=final_state.get("final_output", {}),
                metadata=final_state.get("execution_metadata", {})
            )

            logger.info(f"Workflow {workflow_id} completed",
                       extra={
                           "workflow_id": workflow_id,
                           "execution_time_ms": execution_time,
                           "success_rate": success_rate
                       })

            return result

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            error_code = "WORKFLOW_EXECUTION_FAILED"

            logger.error(f"Workflow {workflow_id} failed",
                        extra={
                            "workflow_id": workflow_id,
                            "error_code": error_code,
                            "execution_time_ms": execution_time
                        })

            # Return failed result
            return WorkflowResult(
                workflow_id=workflow_id,
                workflow_type=request.workflow_type.value,
                status=WorkflowStatus.FAILED.value,
                documents_processed=0,
                analysis_results=[],
                compliance_results=[],
                execution_time_ms=execution_time,
                success_rate=0.0,
                errors=[str(e)],
                warnings=[],
                final_output={},
                metadata={"error_code": error_code}
            )

        finally:
            # Clean up active workflow
            async with self._lock:
                self.active_workflows.pop(workflow_id, None)

    # Workflow node implementations
    async def _initialize_workflow(self, state: WorkflowState) -> WorkflowState:
        """Initialize workflow execution."""
        state["current_step"] = "initialize"
        state["step_history"].append("initialize")
        state["execution_metadata"]["initialized_at"] = datetime.utcnow().isoformat()

        # Initialize services
        await self.rag_pipeline.initialize()
        await self.agent_system.initialize()

        logger.info(f"Workflow {state['workflow_id']} initialized")
        return state

    async def _validate_input(self, state: WorkflowState) -> WorkflowState:
        """Validate workflow input."""
        state["current_step"] = "validate_input"
        state["step_history"].append("validate_input")

        try:
            input_data = state["input_data"]
            documents = input_data.get("documents", [])

            if not documents:
                state["errors"].append("No documents provided for processing")
                state["status"] = WorkflowStatus.FAILED.value
                return state

            # Validate each document
            for i, doc_data in enumerate(documents):
                if not doc_data.get("content"):
                    state["errors"].append(f"Document {i} has no content")
                if not doc_data.get("title"):
                    state["warnings"].append(f"Document {i} has no title")

            logger.info(f"Input validation completed for workflow {state['workflow_id']}")
            return state

        except Exception as e:
            state["errors"].append(f"Input validation failed: {str(e)}")
            state["status"] = WorkflowStatus.FAILED.value
            return state

    async def _process_documents(self, state: WorkflowState) -> WorkflowState:
        """Process documents through RAG pipeline."""
        state["current_step"] = "process_documents"
        state["step_history"].append("process_documents")

        try:
            documents_data = state["input_data"]["documents"]
            processed_docs = []

            for doc_data in documents_data:
                # Convert to DocumentInput
                doc_input = DocumentInput(**doc_data)

                # Process through RAG pipeline
                result = await self.rag_pipeline.process_document(doc_input)

                processed_docs.append({
                    "document_id": result.document_id,
                    "original_data": doc_data,
                    "processing_result": result.dict(),
                    "processed_at": datetime.utcnow().isoformat()
                })

            state["processed_documents"] = processed_docs
            logger.info(f"Processed {len(processed_docs)} documents for workflow {state['workflow_id']}")
            return state

        except Exception as e:
            state["errors"].append(f"Document processing failed: {str(e)}")
            return state

    async def _generate_embeddings(self, state: WorkflowState) -> WorkflowState:
        """Generate embeddings for processed documents."""
        state["current_step"] = "generate_embeddings"
        state["step_history"].append("generate_embeddings")

        # Embeddings are generated as part of document processing
        # This step is for any additional embedding operations

        logger.info(f"Embeddings generated for workflow {state['workflow_id']}")
        return state

    async def _store_vectors(self, state: WorkflowState) -> WorkflowState:
        """Store vectors in vector database."""
        state["current_step"] = "store_vectors"
        state["step_history"].append("store_vectors")

        # Vectors are stored as part of document processing
        # This step is for any additional storage operations

        logger.info(f"Vectors stored for workflow {state['workflow_id']}")
        return state

    async def _analyze_content(self, state: WorkflowState) -> WorkflowState:
        """Analyze document content using AI agents."""
        state["current_step"] = "analyze_content"
        state["step_history"].append("analyze_content")

        try:
            analysis_types = state["input_data"].get("analysis_types", ["compliance_check"])
            analysis_results = []

            for doc in state["processed_documents"]:
                doc_content = doc["original_data"]["content"]
                doc_type = doc["original_data"].get("document_type", "unknown")

                for analysis_type in analysis_types:
                    # Create analysis request
                    request = DocumentAnalysisRequest(
                        document_content=doc_content,
                        document_type=doc_type,
                        analysis_type=analysis_type,
                        priority=TaskPriority(state["input_data"].get("priority", "medium"))
                    )

                    # Perform analysis
                    result = await self.agent_system.analyze_document(request)

                    analysis_results.append({
                        "document_id": doc["document_id"],
                        "analysis_type": analysis_type,
                        "result": result.dict(),
                        "analyzed_at": datetime.utcnow().isoformat()
                    })

            state["analysis_results"] = analysis_results
            logger.info(f"Content analysis completed for workflow {state['workflow_id']}")
            return state

        except Exception as e:
            state["errors"].append(f"Content analysis failed: {str(e)}")
            return state

    async def _compliance_check(self, state: WorkflowState) -> WorkflowState:
        """Perform compliance checking."""
        state["current_step"] = "compliance_check"
        state["step_history"].append("compliance_check")

        try:
            frameworks = state["input_data"].get("compliance_frameworks", ["fda"])
            compliance_results = []

            for doc in state["processed_documents"]:
                doc_content = doc["original_data"]["content"]

                for framework in frameworks:
                    # Create compliance check request
                    request = ComplianceCheckRequest(
                        document_content=doc_content,
                        regulation_framework=framework,
                        check_categories=["documentation", "process", "quality"],
                        severity_threshold="medium"
                    )

                    # Perform compliance check
                    result = await self.agent_system.check_compliance(request)

                    compliance_results.append({
                        "document_id": doc["document_id"],
                        "framework": framework,
                        "result": result.dict(),
                        "checked_at": datetime.utcnow().isoformat()
                    })

            state["compliance_results"] = compliance_results
            logger.info(f"Compliance check completed for workflow {state['workflow_id']}")
            return state

        except Exception as e:
            state["errors"].append(f"Compliance check failed: {str(e)}")
            return state

    async def _generate_summary(self, state: WorkflowState) -> WorkflowState:
        """Generate workflow summary."""
        state["current_step"] = "generate_summary"
        state["step_history"].append("generate_summary")

        try:
            summary = {
                "workflow_id": state["workflow_id"],
                "workflow_type": state["workflow_type"],
                "documents_processed": len(state["processed_documents"]),
                "analyses_performed": len(state["analysis_results"]),
                "compliance_checks": len(state["compliance_results"]),
                "errors_count": len(state["errors"]),
                "warnings_count": len(state["warnings"]),
                "execution_steps": state["step_history"],
                "generated_at": datetime.utcnow().isoformat()
            }

            state["final_output"]["summary"] = summary
            logger.info(f"Summary generated for workflow {state['workflow_id']}")
            return state

        except Exception as e:
            state["errors"].append(f"Summary generation failed: {str(e)}")
            return state

    async def _finalize_results(self, state: WorkflowState) -> WorkflowState:
        """Finalize workflow results."""
        state["current_step"] = "finalize_results"
        state["step_history"].append("finalize_results")
        state["end_time"] = time.time()

        if not state["errors"]:
            state["status"] = WorkflowStatus.COMPLETED.value
        else:
            state["status"] = WorkflowStatus.FAILED.value

        logger.info(f"Workflow {state['workflow_id']} finalized with status {state['status']}")
        return state

    async def _handle_error(self, state: WorkflowState) -> WorkflowState:
        """Handle workflow errors."""
        state["current_step"] = "handle_error"
        state["step_history"].append("handle_error")
        state["status"] = WorkflowStatus.FAILED.value
        state["end_time"] = time.time()

        logger.error(f"Workflow {state['workflow_id']} failed with errors: {state['errors']}")
        return state

    def _should_continue_or_error(self, state: WorkflowState) -> str:
        """Determine if workflow should continue or handle error."""
        if state["errors"]:
            return "error"
        return "continue"

    # Additional workflow node implementations (simplified for brevity)
    async def _extract_content(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "extract_content"
        state["step_history"].append("extract_content")
        return state

    async def _risk_assessment(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "risk_assessment"
        state["step_history"].append("risk_assessment")
        return state

    async def _regulatory_review(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "regulatory_review"
        state["step_history"].append("regulatory_review")
        return state

    async def _consolidate_findings(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "consolidate_findings"
        state["step_history"].append("consolidate_findings")
        return state

    async def _generate_compliance_report(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "generate_compliance_report"
        state["step_history"].append("generate_compliance_report")
        return state

    async def _identify_risks(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "identify_risks"
        state["step_history"].append("identify_risks")
        return state

    async def _assess_severity(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "assess_severity"
        state["step_history"].append("assess_severity")
        return state

    async def _evaluate_impact(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "evaluate_impact"
        state["step_history"].append("evaluate_impact")
        return state

    async def _recommend_mitigations(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "recommend_mitigations"
        state["step_history"].append("recommend_mitigations")
        return state

    async def _generate_risk_report(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "generate_risk_report"
        state["step_history"].append("generate_risk_report")
        return state

    async def _regulatory_analysis(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "regulatory_analysis"
        state["step_history"].append("regulatory_analysis")
        return state

    async def _guideline_compliance(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "guideline_compliance"
        state["step_history"].append("guideline_compliance")
        return state

    async def _submission_readiness(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "submission_readiness"
        state["step_history"].append("submission_readiness")
        return state

    async def _generate_regulatory_report(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "generate_regulatory_report"
        state["step_history"].append("generate_regulatory_report")
        return state

    async def _batch_process_documents(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "batch_process_documents"
        state["step_history"].append("batch_process_documents")
        return state

    async def _parallel_analysis(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "parallel_analysis"
        state["step_history"].append("parallel_analysis")
        return state

    async def _aggregate_results(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "aggregate_results"
        state["step_history"].append("aggregate_results")
        return state

    async def _generate_batch_report(self, state: WorkflowState) -> WorkflowState:
        state["current_step"] = "generate_batch_report"
        state["step_history"].append("generate_batch_report")
        return state

    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific workflow."""
        async with self._lock:
            workflow_state = self.active_workflows.get(workflow_id)

        if not workflow_state:
            return None

        return {
            "workflow_id": workflow_id,
            "status": workflow_state["status"],
            "current_step": workflow_state["current_step"],
            "progress": len(workflow_state["step_history"]),
            "errors_count": len(workflow_state["errors"]),
            "warnings_count": len(workflow_state["warnings"]),
            "documents_processed": len(workflow_state["processed_documents"]),
            "start_time": workflow_state["start_time"]
        }

    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a running workflow."""
        async with self._lock:
            workflow_state = self.active_workflows.get(workflow_id)

        if not workflow_state:
            return False

        workflow_state["status"] = WorkflowStatus.CANCELLED.value
        workflow_state["end_time"] = time.time()

        logger.info(f"Workflow {workflow_id} cancelled")
        return True

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on workflow orchestrator."""
        try:
            # Check component health
            rag_health = await self.rag_pipeline.health_check()
            agent_health = await self.agent_system.health_check()

            return {
                "status": "healthy",
                "orchestrator_ready": True,
                "workflows_available": len(self.workflows),
                "active_workflows": len(self.active_workflows),
                "rag_pipeline": rag_health,
                "agent_system": agent_health,
                "checkpoints_enabled": self.config.enable_checkpoints,
                "last_check": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error("Workflow orchestrator health check failed",
                        extra={"error_code": "HEALTH_CHECK_FAILED"})
            return {
                "status": "unhealthy",
                "orchestrator_ready": False,
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }

# Global orchestrator instance
_workflow_orchestrator: Optional[PharmaceuticalWorkflowOrchestrator] = None

def get_workflow_orchestrator() -> PharmaceuticalWorkflowOrchestrator:
    """Get the global workflow orchestrator instance."""
    global _workflow_orchestrator
    if _workflow_orchestrator is None:
        _workflow_orchestrator = PharmaceuticalWorkflowOrchestrator()
    return _workflow_orchestrator

# Export main classes and functions
__all__ = [
    "PharmaceuticalWorkflowOrchestrator",
    "DocumentProcessingRequest",
    "WorkflowResult",
    "WorkflowConfig",
    "WorkflowError",
    "WorkflowStatus",
    "WorkflowType",
    "get_workflow_orchestrator"
]
