"""CrewAI Multi-Agent System for Pharmaceutical Compliance

Implements specialized AI agents for pharmaceutical document analysis, compliance checking,
and regulatory guidance using CrewAI framework with production-ready error handling.
"""

import asyncio
import logging
import time
from typing import List, Optional, Dict, Any, Union, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import json

from pydantic import BaseModel, Field, field_validator, ConfigDict
from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from langchain.llms.base import LLM
from langchain.callbacks.manager import CallbackManagerForLLMRun

# Import our RAG pipeline
from .rag_pipeline import get_rag_pipeline, QueryRequest, RAGError

# Configure structured logging
logger = logging.getLogger(__name__)

class AgentError(Exception):
    """Custom exception for agent operations."""
    def __init__(self, message: str, error_code: str, agent_name: Optional[str] = None):
        self.message = message
        self.error_code = error_code
        self.agent_name = agent_name
        super().__init__(self.message)

class AgentStatus(str, Enum):
    """Agent status enumeration."""
    NOT_INITIALIZED = "not_initialized"
    INITIALIZING = "initializing"
    READY = "ready"
    BUSY = "busy"
    ERROR = "error"

class TaskPriority(str, Enum):
    """Task priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass(frozen=True)
class AgentConfig:
    """Configuration for CrewAI agents."""
    max_execution_time: int = 300  # 5 minutes
    max_iterations: int = 10
    temperature: float = 0.1
    max_tokens: int = 2000
    enable_memory: bool = True
    verbose: bool = False

class DocumentAnalysisRequest(BaseModel):
    """Request for document analysis."""
    document_content: str = Field(..., description="Document content to analyze")
    document_type: str = Field(..., description="Type of document (e.g., 'clinical_trial', 'regulatory_filing')")
    analysis_type: str = Field(..., description="Type of analysis requested")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="Task priority")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context")

    @field_validator('document_content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """Validate document content."""
        if not v or not v.strip():
            raise ValueError("Document content cannot be empty")
        if len(v) > 500_000:  # 500KB limit
            raise ValueError("Document content too large (max 500KB)")
        return v.strip()

    @field_validator('analysis_type')
    @classmethod
    def validate_analysis_type(cls, v: str) -> str:
        """Validate analysis type."""
        valid_types = {
            'compliance_check', 'risk_assessment', 'regulatory_review',
            'clinical_data_analysis', 'adverse_event_analysis', 'quality_review'
        }
        if v not in valid_types:
            raise ValueError(f"Invalid analysis type. Must be one of: {valid_types}")
        return v

class ComplianceCheckRequest(BaseModel):
    """Request for compliance checking."""
    document_content: str = Field(..., description="Document content to check")
    regulation_framework: str = Field(..., description="Regulatory framework to check against")
    check_categories: List[str] = Field(..., description="Categories to check")
    severity_threshold: str = Field(default="medium", description="Minimum severity to report")

    @field_validator('regulation_framework')
    @classmethod
    def validate_framework(cls, v: str) -> str:
        """Validate regulatory framework."""
        valid_frameworks = {'fda', 'ema', 'ich', 'gcp', 'gmp', 'gdp'}
        if v.lower() not in valid_frameworks:
            raise ValueError(f"Invalid framework. Must be one of: {valid_frameworks}")
        return v.lower()

class AgentResponse(BaseModel):
    """Response from an agent."""
    agent_name: str
    task_id: str
    result: Dict[str, Any]
    execution_time_ms: float
    status: str
    confidence_score: Optional[float] = None
    warnings: List[str] = Field(default_factory=list)

class CrewExecutionResult(BaseModel):
    """Result from crew execution."""
    crew_id: str
    task_results: List[AgentResponse]
    total_execution_time_ms: float
    success: bool
    final_output: Dict[str, Any]
    errors: List[str] = Field(default_factory=list)

class RAGTool(BaseTool):
    """Custom tool for RAG pipeline integration."""

    name: str = "rag_search"
    description: str = "Search pharmaceutical knowledge base for relevant information"

    def _run(self, query: str, max_results: int = 5) -> str:
        """Execute RAG search synchronously."""
        try:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self._async_run(query, max_results))
                return result
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"RAG tool execution failed: {str(e)}")
            return f"Error searching knowledge base: {str(e)}"

    async def _async_run(self, query: str, max_results: int = 5) -> str:
        """Execute RAG search asynchronously."""
        try:
            rag_pipeline = get_rag_pipeline()
            request = QueryRequest(
                query=query,
                max_results=max_results,
                similarity_threshold=0.7
            )

            response = await rag_pipeline.query(request)

            if not response.results:
                return "No relevant information found in knowledge base."

            # Format results
            formatted_results = []
            for result in response.results:
                formatted_results.append(
                    f"Source: {result.metadata.source}\n"
                    f"Content: {result.content[:500]}...\n"
                    f"Relevance: {result.score:.2f}\n"
                )

            return "\n---\n".join(formatted_results)

        except Exception as e:
            logger.error(f"RAG search failed: {str(e)}")
            return f"Knowledge base search error: {str(e)}"

class MockLLM(LLM):
    """Mock LLM for development and testing."""

    @property
    def _llm_type(self) -> str:
        return "mock"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """Mock LLM call that returns a structured response."""
        # Simple mock response based on prompt content
        if "compliance" in prompt.lower():
            return "Based on regulatory guidelines, this document appears to be compliant with FDA requirements. No major issues identified."
        elif "risk" in prompt.lower():
            return "Risk assessment indicates low to medium risk profile. Recommend additional monitoring for safety parameters."
        elif "analysis" in prompt.lower():
            return "Document analysis complete. Key findings: regulatory requirements met, data quality acceptable, recommendations provided."
        else:
            return "Analysis completed successfully. Please refer to detailed findings in the report."

class PharmaceuticalAgentSystem:
    """CrewAI-based multi-agent system for pharmaceutical compliance."""

    def __init__(self, config: Optional[AgentConfig] = None):
        """Initialize the agent system."""
        self.config = config or AgentConfig()
        self.status = AgentStatus.NOT_INITIALIZED
        self._lock = asyncio.Lock()
        self.agents: Dict[str, Agent] = {}
        self.tools: List[BaseTool] = []
        self.llm = MockLLM()  # Replace with actual LLM in production

        logger.info("Pharmaceutical agent system initialized")

    async def initialize(self) -> None:
        """Initialize all agents and tools."""
        async with self._lock:
            if self.status == AgentStatus.READY:
                return

            try:
                self.status = AgentStatus.INITIALIZING
                logger.info("Initializing pharmaceutical agent system...")

                # Initialize tools
                self.tools = [RAGTool()]

                # Create specialized agents
                await self._create_agents()

                self.status = AgentStatus.READY
                logger.info("Agent system initialized successfully")

            except Exception as e:
                self.status = AgentStatus.ERROR
                error_code = "AGENT_SYSTEM_INIT_FAILED"
                logger.error("Failed to initialize agent system",
                           extra={"error_code": error_code})
                raise AgentError(f"Agent system initialization failed: {str(e)}", error_code)

    async def _create_agents(self) -> None:
        """Create specialized pharmaceutical agents."""
        try:
            # Compliance Specialist Agent
            self.agents["compliance_specialist"] = Agent(
                role="Pharmaceutical Compliance Specialist",
                goal="Ensure pharmaceutical documents comply with regulatory requirements",
                backstory="Expert in FDA, EMA, and ICH guidelines with 15+ years experience in pharmaceutical compliance.",
                tools=self.tools,
                llm=self.llm,
                verbose=self.config.verbose,
                memory=self.config.enable_memory
            )

            # Risk Assessment Agent
            self.agents["risk_assessor"] = Agent(
                role="Pharmaceutical Risk Assessment Specialist",
                goal="Identify and evaluate risks in pharmaceutical processes and documents",
                backstory="Specialized in pharmaceutical risk management, quality assurance, and safety evaluation.",
                tools=self.tools,
                llm=self.llm,
                verbose=self.config.verbose,
                memory=self.config.enable_memory
            )

            # Regulatory Affairs Agent
            self.agents["regulatory_affairs"] = Agent(
                role="Regulatory Affairs Specialist",
                goal="Provide guidance on regulatory requirements and submission strategies",
                backstory="Expert in global pharmaceutical regulations, submission processes, and regulatory strategy.",
                tools=self.tools,
                llm=self.llm,
                verbose=self.config.verbose,
                memory=self.config.enable_memory
            )

            # Clinical Data Analyst
            self.agents["clinical_analyst"] = Agent(
                role="Clinical Data Analysis Specialist",
                goal="Analyze clinical trial data and ensure data integrity",
                backstory="Biostatistician with expertise in clinical trial design, data analysis, and regulatory submissions.",
                tools=self.tools,
                llm=self.llm,
                verbose=self.config.verbose,
                memory=self.config.enable_memory
            )

            # Quality Assurance Agent
            self.agents["quality_assurance"] = Agent(
                role="Quality Assurance Specialist",
                goal="Ensure quality standards and GMP compliance",
                backstory="Quality professional with deep knowledge of GMP, validation, and quality systems.",
                tools=self.tools,
                llm=self.llm,
                verbose=self.config.verbose,
                memory=self.config.enable_memory
            )

            logger.info(f"Created {len(self.agents)} specialized agents")

        except Exception as e:
            logger.error("Failed to create agents", extra={"error_code": "AGENT_CREATION_FAILED"})
            raise

    async def analyze_document(self, request: DocumentAnalysisRequest) -> AgentResponse:
        """Analyze a document using appropriate specialist agent."""
        start_time = time.time()

        if self.status != AgentStatus.READY:
            await self.initialize()

        try:
            # Select appropriate agent based on analysis type
            agent_name = self._select_agent_for_analysis(request.analysis_type)
            agent = self.agents.get(agent_name)

            if not agent:
                raise AgentError(f"Agent {agent_name} not found", "AGENT_NOT_FOUND", agent_name)

            # Create task
            task = Task(
                description=f"""
                Analyze the following {request.document_type} document for {request.analysis_type}:

                Document Content:
                {request.document_content[:2000]}...

                Analysis Requirements:
                - Type: {request.analysis_type}
                - Priority: {request.priority.value}
                - Context: {json.dumps(request.context, indent=2)}

                Please provide a comprehensive analysis including:
                1. Key findings
                2. Compliance status
                3. Risk assessment
                4. Recommendations
                5. Action items
                """,
                agent=agent,
                expected_output="Detailed analysis report with findings, compliance status, and recommendations"
            )

            # Execute task
            crew = Crew(
                agents=[agent],
                tasks=[task],
                process=Process.sequential,
                verbose=self.config.verbose
            )

            self.status = AgentStatus.BUSY
            result = crew.kickoff()
            self.status = AgentStatus.READY

            execution_time = (time.time() - start_time) * 1000

            # Parse result
            parsed_result = self._parse_agent_result(result, request.analysis_type)

            logger.info(f"Document analysis completed",
                       extra={
                           "agent_name": agent_name,
                           "analysis_type": request.analysis_type,
                           "execution_time_ms": execution_time
                       })

            return AgentResponse(
                agent_name=agent_name,
                task_id=f"analysis_{int(time.time())}",
                result=parsed_result,
                execution_time_ms=execution_time,
                status="completed",
                confidence_score=0.85  # Mock confidence score
            )

        except Exception as e:
            self.status = AgentStatus.READY  # Reset status
            error_code = "DOCUMENT_ANALYSIS_FAILED"
            logger.error("Document analysis failed",
                        extra={"error_code": error_code, "analysis_type": request.analysis_type})
            raise AgentError(f"Document analysis failed: {str(e)}", error_code)

    def _select_agent_for_analysis(self, analysis_type: str) -> str:
        """Select the most appropriate agent for the analysis type."""
        agent_mapping = {
            'compliance_check': 'compliance_specialist',
            'risk_assessment': 'risk_assessor',
            'regulatory_review': 'regulatory_affairs',
            'clinical_data_analysis': 'clinical_analyst',
            'adverse_event_analysis': 'clinical_analyst',
            'quality_review': 'quality_assurance'
        }

        return agent_mapping.get(analysis_type, 'compliance_specialist')

    def _parse_agent_result(self, result: Any, analysis_type: str) -> Dict[str, Any]:
        """Parse and structure agent result."""
        try:
            # Convert result to string if needed
            result_text = str(result) if not isinstance(result, str) else result

            # Basic parsing - in production, use more sophisticated NLP
            parsed = {
                "analysis_type": analysis_type,
                "summary": result_text[:500] + "..." if len(result_text) > 500 else result_text,
                "full_analysis": result_text,
                "key_findings": self._extract_key_findings(result_text),
                "compliance_status": self._extract_compliance_status(result_text),
                "risk_level": self._extract_risk_level(result_text),
                "recommendations": self._extract_recommendations(result_text),
                "timestamp": datetime.utcnow().isoformat()
            }

            return parsed

        except Exception as e:
            logger.warning(f"Failed to parse agent result: {str(e)}")
            return {
                "analysis_type": analysis_type,
                "raw_result": str(result),
                "parsing_error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    def _extract_key_findings(self, text: str) -> List[str]:
        """Extract key findings from analysis text."""
        # Simple extraction - replace with NLP in production
        findings = []
        if "compliant" in text.lower():
            findings.append("Document appears to be compliant")
        if "risk" in text.lower():
            findings.append("Risk factors identified")
        if "recommend" in text.lower():
            findings.append("Recommendations provided")
        return findings or ["Analysis completed"]

    def _extract_compliance_status(self, text: str) -> str:
        """Extract compliance status from analysis text."""
        text_lower = text.lower()
        if "non-compliant" in text_lower or "violation" in text_lower:
            return "non_compliant"
        elif "compliant" in text_lower:
            return "compliant"
        else:
            return "requires_review"

    def _extract_risk_level(self, text: str) -> str:
        """Extract risk level from analysis text."""
        text_lower = text.lower()
        if "high risk" in text_lower or "critical" in text_lower:
            return "high"
        elif "medium risk" in text_lower or "moderate" in text_lower:
            return "medium"
        elif "low risk" in text_lower:
            return "low"
        else:
            return "unknown"

    def _extract_recommendations(self, text: str) -> List[str]:
        """Extract recommendations from analysis text."""
        # Simple extraction - replace with NLP in production
        recommendations = []
        if "monitor" in text.lower():
            recommendations.append("Continue monitoring")
        if "review" in text.lower():
            recommendations.append("Additional review required")
        if "update" in text.lower():
            recommendations.append("Documentation updates needed")
        return recommendations or ["No specific recommendations"]

    async def check_compliance(self, request: ComplianceCheckRequest) -> AgentResponse:
        """Perform compliance checking using compliance specialist."""
        start_time = time.time()

        if self.status != AgentStatus.READY:
            await self.initialize()

        try:
            agent = self.agents["compliance_specialist"]

            # Create compliance check task
            task = Task(
                description=f"""
                Perform a comprehensive compliance check on the following document:

                Document Content:
                {request.document_content[:2000]}...

                Compliance Requirements:
                - Regulatory Framework: {request.regulation_framework.upper()}
                - Check Categories: {', '.join(request.check_categories)}
                - Severity Threshold: {request.severity_threshold}

                Please check for:
                1. Regulatory compliance
                2. Documentation completeness
                3. Process adherence
                4. Quality standards
                5. Risk factors

                Provide specific findings for each category and overall compliance status.
                """,
                agent=agent,
                expected_output="Detailed compliance report with category-specific findings and overall status"
            )

            # Execute compliance check
            crew = Crew(
                agents=[agent],
                tasks=[task],
                process=Process.sequential,
                verbose=self.config.verbose
            )

            self.status = AgentStatus.BUSY
            result = crew.kickoff()
            self.status = AgentStatus.READY

            execution_time = (time.time() - start_time) * 1000

            # Parse compliance result
            parsed_result = self._parse_compliance_result(result, request)

            logger.info(f"Compliance check completed",
                       extra={
                           "framework": request.regulation_framework,
                           "categories": len(request.check_categories),
                           "execution_time_ms": execution_time
                       })

            return AgentResponse(
                agent_name="compliance_specialist",
                task_id=f"compliance_{int(time.time())}",
                result=parsed_result,
                execution_time_ms=execution_time,
                status="completed",
                confidence_score=0.90
            )

        except Exception as e:
            self.status = AgentStatus.READY
            error_code = "COMPLIANCE_CHECK_FAILED"
            logger.error("Compliance check failed",
                        extra={"error_code": error_code, "framework": request.regulation_framework})
            raise AgentError(f"Compliance check failed: {str(e)}", error_code, "compliance_specialist")

    def _parse_compliance_result(self, result: Any, request: ComplianceCheckRequest) -> Dict[str, Any]:
        """Parse compliance check result."""
        result_text = str(result)

        return {
            "regulation_framework": request.regulation_framework,
            "check_categories": request.check_categories,
            "overall_compliance": self._extract_compliance_status(result_text),
            "findings": self._extract_key_findings(result_text),
            "violations": self._extract_violations(result_text),
            "recommendations": self._extract_recommendations(result_text),
            "severity_assessment": self._assess_severity(result_text),
            "full_report": result_text,
            "timestamp": datetime.utcnow().isoformat()
        }

    def _extract_violations(self, text: str) -> List[str]:
        """Extract compliance violations from text."""
        violations = []
        text_lower = text.lower()

        if "violation" in text_lower:
            violations.append("Regulatory violation identified")
        if "non-compliant" in text_lower:
            violations.append("Non-compliance detected")
        if "missing" in text_lower:
            violations.append("Missing documentation")

        return violations

    def _assess_severity(self, text: str) -> str:
        """Assess severity of compliance issues."""
        text_lower = text.lower()

        if "critical" in text_lower or "severe" in text_lower:
            return "critical"
        elif "major" in text_lower or "significant" in text_lower:
            return "major"
        elif "minor" in text_lower:
            return "minor"
        else:
            return "low"

    async def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents."""
        try:
            agent_statuses = {}
            for name, agent in self.agents.items():
                agent_statuses[name] = {
                    "role": agent.role,
                    "goal": agent.goal,
                    "tools_count": len(agent.tools) if agent.tools else 0,
                    "memory_enabled": agent.memory if hasattr(agent, 'memory') else False
                }

            return {
                "system_status": self.status.value,
                "agents": agent_statuses,
                "total_agents": len(self.agents),
                "tools_available": len(self.tools),
                "config": {
                    "max_execution_time": self.config.max_execution_time,
                    "max_iterations": self.config.max_iterations,
                    "temperature": self.config.temperature
                }
            }

        except Exception as e:
            logger.error("Failed to get agent status", extra={"error_code": "STATUS_FAILED"})
            return {"status": "error", "error": str(e)}

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on agent system."""
        try:
            if self.status != AgentStatus.READY:
                await self.initialize()

            # Test basic functionality
            test_request = DocumentAnalysisRequest(
                document_content="Test document for health check",
                document_type="test",
                analysis_type="compliance_check"
            )

            test_response = await self.analyze_document(test_request)

            return {
                "status": "healthy",
                "system_ready": True,
                "agents_count": len(self.agents),
                "test_execution_time_ms": test_response.execution_time_ms,
                "last_check": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error("Agent system health check failed", extra={"error_code": "HEALTH_CHECK_FAILED"})
            return {
                "status": "unhealthy",
                "system_ready": False,
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }

# Global agent system instance
_agent_system: Optional[PharmaceuticalAgentSystem] = None

def get_agent_system() -> PharmaceuticalAgentSystem:
    """Get the global agent system instance."""
    global _agent_system
    if _agent_system is None:
        _agent_system = PharmaceuticalAgentSystem()
    return _agent_system

# Export main classes and functions
__all__ = [
    "PharmaceuticalAgentSystem",
    "DocumentAnalysisRequest",
    "ComplianceCheckRequest",
    "AgentResponse",
    "CrewExecutionResult",
    "AgentConfig",
    "AgentError",
    "AgentStatus",
    "TaskPriority",
    "get_agent_system"
]
